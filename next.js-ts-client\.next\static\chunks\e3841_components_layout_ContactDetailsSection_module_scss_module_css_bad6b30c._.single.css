/* [project]/app/(shop)/(checkout-process)/components/layout/ContactDetailsSection.module.scss.module.css [app-client] (css) */
.ContactDetailsSection-module-scss-module__q_i1eq__contact_info {
  flex-direction: column;
  gap: 1rem;
  display: flex;
}

@media (min-width: 768px) {
  .ContactDetailsSection-module-scss-module__q_i1eq__contact_info {
    flex-direction: row;
    justify-content: space-between;
  }
}

.ContactDetailsSection-module-scss-module__q_i1eq__contact_details h3 {
  color: #333;
  margin-bottom: .5rem;
}

.ContactDetailsSection-module-scss-module__q_i1eq__contact_details p {
  color: #666;
  margin: .25rem 0;
}

.ContactDetailsSection-module-scss-module__q_i1eq__shipping_address h3 {
  color: #333;
  margin-bottom: .5rem;
}

.ContactDetailsSection-module-scss-module__q_i1eq__shipping_address address {
  color: #666;
  font-style: normal;
  line-height: 1.4;
}

/*# sourceMappingURL=e3841_components_layout_ContactDetailsSection_module_scss_module_css_bad6b30c._.single.css.map*/