import { CartItemShape, CustomerShape } from "./store-types"

export interface FetchPaginatedResponse<T> {
  count: number
  next: number | null
  previous: number | null
  results: T[]
}

// export interface FetchResponse<T> {
//   data?: T[]
// }

export interface PaymentOptionsShape {
  id: number
  name: string
  is_active: boolean
}

export interface CartShape {
  id: string
  customer: CustomerShape | null
  cart_items: CartItemShape[]
  total_price: number
  // shipping_cost: number
  // grand_total: number
  item_count: number
  cart_weight: number
}

export interface ErrorResponse {
  [key: string]: string[]
}

export interface AttributeValue {
  id: number
  attribute: {
    id: number
    title: string
  }
  attribute_value: string
  for_filtering: boolean
  is_active: boolean
  selectable: boolean
}


export interface FilterOptionsShape {
  price_range: {
    min: number
    max: number
  }
  condition: [string, string][]
  brands: {
    id: number
    slug: string
  }[]
  attribute_filters: {
    [key: string]: string[]
  }
}