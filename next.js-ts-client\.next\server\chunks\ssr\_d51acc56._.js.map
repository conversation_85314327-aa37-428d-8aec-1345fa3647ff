{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/stores/order-store.ts"], "sourcesContent": ["import { create } from 'zustand'\r\nimport { persist } from 'zustand/middleware'\r\n\r\ninterface OrderStoreShape {\r\n  orderId: number | null\r\n  setOrderId: (id: number | null) => void\r\n  // Define other state properties and actions if you uncomment the code for order and customer\r\n  // order: Order;\r\n  // setOrder: (newOrder: Partial<Order>) => void;\r\n  // clearOrder: () => void;\r\n  // customer: Customer;\r\n}\r\n\r\nconst orderStore = create<OrderStoreShape>()(\r\n  persist(\r\n    (set) => ({\r\n      // order: {\r\n      //   orderedProducts: [],\r\n      //   subtotal: 0\r\n      // },\r\n      // setOrder: (newOrder) => set((state) => ({ order: { ...state.order, ...newOrder } })),\r\n      // clearOrder: () => set({ order: { orderedProducts: [], subtotal: 0 } }),\r\n      // customer: {},\r\n      orderId: null,\r\n      setOrderId: (id) => set({ orderId: id }),\r\n    }),\r\n    {\r\n      name: 'order_store',\r\n      // storage: createJSONStorage(() => sessionStorage) // Specify the storage engine (localStorage or sessionStorage)\r\n    }\r\n  )\r\n)\r\n\r\nexport default orderStore\r\n\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAYA,MAAM,aAAa,IAAA,kJAAM,IACvB,IAAA,wJAAO,EACL,CAAC,MAAQ,CAAC;QACR,WAAW;QACX,yBAAyB;QACzB,gBAAgB;QAChB,KAAK;QACL,wFAAwF;QACxF,0EAA0E;QAC1E,gBAAgB;QAChB,SAAS;QACT,YAAY,CAAC,KAAO,IAAI;gBAAE,SAAS;YAAG;IACxC,CAAC,GACD;IACE,MAAM;AAER;uCAIW", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/hooks/order-hooks.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'\r\nimport APIClient from '../lib/api-client'\r\nimport { createOrderShape, OrderShape } from '../types/order-types'\r\nimport { CACHE_KEY_ORDER_ITEMS, CACHE_KEY_ORDERS } from '../constants/constants'\r\nimport orderStore from '../stores/order-store'\r\nimport cartStore from '../stores/cart-store'\r\nimport { useState } from 'react'\r\n\r\n\r\nexport const useOrder = (orderId: number) => {\r\n\r\n  const apiClient = new APIClient<OrderShape>(`/orders/${orderId}`)\r\n\r\n  return useQuery({\r\n    // queryKey: [CACHE_KEY_ORDER_ITEMS, orderId],\r\n    queryKey: [CACHE_KEY_ORDER_ITEMS],\r\n    queryFn: () => apiClient.get(),\r\n    staleTime: 0,\r\n  })\r\n\r\n}\r\n\r\nexport const useGetAllOrders = (page: number, queryString: string = '') => {\r\n  const apiClient = new APIClient<OrderShape>(`/orders/`)\r\n  const queryParams = new URLSearchParams(queryString)\r\n\r\n  queryParams.set('page', page.toString())\r\n\r\n  return useQuery({\r\n    queryKey: [CACHE_KEY_ORDERS, page, queryString],\r\n    queryFn: () => apiClient.getAll({ params: Object.fromEntries(queryParams) }),\r\n    // staleTime: 1000 * 60 * 5, // 5 minutes\r\n  })\r\n}\r\n\r\nexport const useCreateOrder = () => {\r\n  // const navigate = useNavigate()\r\n  const { setOrderId } = orderStore()\r\n  const { setCartId } = cartStore()\r\n\r\n  const apiClient = new APIClient<OrderShape, createOrderShape>(`/orders/`)\r\n\r\n  const createOrder = useMutation<OrderShape, Error, createOrderShape>({\r\n    mutationFn: (data) => apiClient.post(data),\r\n    onSuccess: (data) => {\r\n      console.log(data)\r\n      setOrderId(data.id)\r\n      setCartId(null)\r\n      localStorage.removeItem('cart_store')\r\n      if (data.id) {\r\n        // navigate(`/checkout/order/${data.id}/`)\r\n      }\r\n    },\r\n  })\r\n\r\n  return { createOrder }\r\n}\r\n\r\nexport const useDeleteOrder = () => {\r\n  const [isModalOpen, setIsModalOpen] = useState(false)\r\n  const [orderToDelete, setOrderToDelete] = useState<number | null>(null)\r\n  const queryClient = useQueryClient()\r\n  const apiClient = new APIClient(`/orders`)\r\n\r\n  const deleteOrder = useMutation({\r\n    mutationFn: (orderId: number) => apiClient.delete(orderId),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: [CACHE_KEY_ORDERS]\r\n      })\r\n    },\r\n  })\r\n\r\n  const handleDeleteClick = (orderId: number) => {\r\n    setOrderToDelete(orderId)\r\n    setIsModalOpen(true)\r\n  }\r\n\r\n  const confirmDelete = () => {\r\n    if (orderToDelete !== null) {\r\n      deleteOrder.mutate(orderToDelete)\r\n      setIsModalOpen(false)\r\n      setOrderToDelete(null)\r\n    }\r\n  }\r\n\r\n  const cancelDelete = () => {\r\n    setIsModalOpen(false)\r\n    setOrderToDelete(null)\r\n  }\r\n\r\n  return {\r\n    deleteOrder,\r\n    isModalOpen,\r\n    handleDeleteClick,\r\n    confirmDelete,\r\n    cancelDelete\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;;;;;;;AAGO,MAAM,WAAW,CAAC;IAEvB,MAAM,YAAY,IAAI,sIAAS,CAAa,CAAC,QAAQ,EAAE,SAAS;IAEhE,OAAO,IAAA,uLAAQ,EAAC;QACd,8CAA8C;QAC9C,UAAU;YAAC,sJAAqB;SAAC;QACjC,SAAS,IAAM,UAAU,GAAG;QAC5B,WAAW;IACb;AAEF;AAEO,MAAM,kBAAkB,CAAC,MAAc,cAAsB,EAAE;IACpE,MAAM,YAAY,IAAI,sIAAS,CAAa,CAAC,QAAQ,CAAC;IACtD,MAAM,cAAc,IAAI,gBAAgB;IAExC,YAAY,GAAG,CAAC,QAAQ,KAAK,QAAQ;IAErC,OAAO,IAAA,uLAAQ,EAAC;QACd,UAAU;YAAC,iJAAgB;YAAE;YAAM;SAAY;QAC/C,SAAS,IAAM,UAAU,MAAM,CAAC;gBAAE,QAAQ,OAAO,WAAW,CAAC;YAAa;IAE5E;AACF;AAEO,MAAM,iBAAiB;IAC5B,iCAAiC;IACjC,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,0IAAU;IACjC,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,yIAAS;IAE/B,MAAM,YAAY,IAAI,sIAAS,CAA+B,CAAC,QAAQ,CAAC;IAExE,MAAM,cAAc,IAAA,6LAAW,EAAsC;QACnE,YAAY,CAAC,OAAS,UAAU,IAAI,CAAC;QACrC,WAAW,CAAC;YACV,QAAQ,GAAG,CAAC;YACZ,WAAW,KAAK,EAAE;YAClB,UAAU;YACV,aAAa,UAAU,CAAC;YACxB,IAAI,KAAK,EAAE,EAAE;YACX,0CAA0C;YAC5C;QACF;IACF;IAEA,OAAO;QAAE;IAAY;AACvB;AAEO,MAAM,iBAAiB;IAC5B,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAgB;IAClE,MAAM,cAAc,IAAA,wMAAc;IAClC,MAAM,YAAY,IAAI,sIAAS,CAAC,CAAC,OAAO,CAAC;IAEzC,MAAM,cAAc,IAAA,6LAAW,EAAC;QAC9B,YAAY,CAAC,UAAoB,UAAU,MAAM,CAAC;QAClD,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC,iJAAgB;iBAAC;YAC9B;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,eAAe;IACjB;IAEA,MAAM,gBAAgB;QACpB,IAAI,kBAAkB,MAAM;YAC1B,YAAY,MAAM,CAAC;YACnB,eAAe;YACf,iBAAiB;QACnB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;QACf,iBAAiB;IACnB;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/hooks/checkout-hooks.ts"], "sourcesContent": ["import { useMutation, useQuery } from \"@tanstack/react-query\"\r\nimport APIClient from \"../lib/api-client\"\r\nimport { useOrder } from \"./order-hooks\"\r\nimport { PaymentOptionsShape } from \"../types/types\"\r\n\r\n\r\ninterface ClientSecretShape {\r\n  client_secret: string\r\n}\r\n\r\ninterface CaptureOrderRequest {\r\n  paypal_order_id: string\r\n}\r\n\r\ninterface CaptureOrderResponse {\r\n  status: string\r\n  payment_status: string\r\n}\r\n\r\ninterface PayPalOrderRequest {\r\n  order_id: number\r\n  amount: string\r\n}\r\n\r\ninterface PayPalOrderResponse {\r\n  id: string\r\n  status: string\r\n}\r\n\r\n\r\nexport const useClientSecret = (orderId: number) => {\r\n  const { data: order } = useOrder(orderId)\r\n  const apiClient = new APIClient<ClientSecretShape>(`payments/payment-intent-secret/`)\r\n\r\n  return useQuery({\r\n    queryKey: ['stripeClientSecret', orderId],\r\n    queryFn: () => apiClient.get({\r\n      params: {\r\n        order_id: orderId\r\n      }\r\n    }),\r\n    // If order is not null or undefined and payment method is Stripe, then enable the query\r\n    enabled: !!order && order.payment_method.slug === 'stripe'\r\n  })\r\n}\r\n\r\nexport const usePaymentOptions = () => {\r\n  const apiClient = new APIClient<PaymentOptionsShape[]>('/payments/payment-options/')\r\n\r\n  const payOptions = useQuery({\r\n    queryKey: ['payment_options'],\r\n    queryFn: apiClient.get,\r\n    // keepPreviousData: true,\r\n    // refetchOnWindowFocus: false,\r\n    staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours\r\n    // initialData:  Here we can add categories as static data\r\n  })\r\n\r\n  return payOptions\r\n}\r\n\r\n// PayPal Hooks\r\nexport const useCaptureOrder = () => {\r\n  const apiClient = new APIClient<CaptureOrderResponse, CaptureOrderRequest>('/payments/capture-paypal-order/')\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CaptureOrderRequest) => apiClient.post(data)\r\n  })\r\n}\r\n\r\nexport const useCreatePayPalOrder = ({ orderId, amount }: { orderId: number, amount: number }) => {\r\n  const apiClient = new APIClient<PayPalOrderResponse, PayPalOrderRequest>('payments/create-paypal-order/')\r\n\r\n  return useMutation({\r\n    mutationFn: () => apiClient.post({\r\n      order_id: orderId,\r\n      amount: amount.toString()\r\n    })\r\n  })\r\n}"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;AACA;AACA;;;;AA4BO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,IAAA,0IAAQ,EAAC;IACjC,MAAM,YAAY,IAAI,sIAAS,CAAoB,CAAC,+BAA+B,CAAC;IAEpF,OAAO,IAAA,uLAAQ,EAAC;QACd,UAAU;YAAC;YAAsB;SAAQ;QACzC,SAAS,IAAM,UAAU,GAAG,CAAC;gBAC3B,QAAQ;oBACN,UAAU;gBACZ;YACF;QACA,wFAAwF;QACxF,SAAS,CAAC,CAAC,SAAS,MAAM,cAAc,CAAC,IAAI,KAAK;IACpD;AACF;AAEO,MAAM,oBAAoB;IAC/B,MAAM,YAAY,IAAI,sIAAS,CAAwB;IAEvD,MAAM,aAAa,IAAA,uLAAQ,EAAC;QAC1B,UAAU;YAAC;SAAkB;QAC7B,SAAS,UAAU,GAAG;QACtB,0BAA0B;QAC1B,+BAA+B;QAC/B,WAAW,KAAK,KAAK,KAAK;IAE5B;IAEA,OAAO;AACT;AAGO,MAAM,kBAAkB;IAC7B,MAAM,YAAY,IAAI,sIAAS,CAA4C;IAE3E,OAAO,IAAA,6LAAW,EAAC;QACjB,YAAY,CAAC,OAA8B,UAAU,IAAI,CAAC;IAC5D;AACF;AAEO,MAAM,uBAAuB,CAAC,EAAE,OAAO,EAAE,MAAM,EAAuC;IAC3F,MAAM,YAAY,IAAI,sIAAS,CAA0C;IAEzE,OAAO,IAAA,6LAAW,EAAC;QACjB,YAAY,IAAM,UAAU,IAAI,CAAC;gBAC/B,UAAU;gBACV,QAAQ,OAAO,QAAQ;YACzB;IACF;AACF", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/checkout/payment-choice/PaymentChoice.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cart\": \"PaymentChoice-module-scss-module__SCceZW__cart\",\n  \"cart_items_quantity\": \"PaymentChoice-module-scss-module__SCceZW__cart_items_quantity\",\n  \"contact_details\": \"PaymentChoice-module-scss-module__SCceZW__contact_details\",\n  \"contact_info\": \"PaymentChoice-module-scss-module__SCceZW__contact_info\",\n  \"logo\": \"PaymentChoice-module-scss-module__SCceZW__logo\",\n  \"payment_options\": \"PaymentChoice-module-scss-module__SCceZW__payment_options\",\n  \"payment_options_stage\": \"PaymentChoice-module-scss-module__SCceZW__payment_options_stage\",\n  \"place_order\": \"PaymentChoice-module-scss-module__SCceZW__place_order\",\n  \"price_summary\": \"PaymentChoice-module-scss-module__SCceZW__price_summary\",\n  \"prices\": \"PaymentChoice-module-scss-module__SCceZW__prices\",\n  \"shipping_address\": \"PaymentChoice-module-scss-module__SCceZW__shipping_address\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/underlay/Underlay.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"underlay\": \"Underlay-module-scss-module__PkWa8a__underlay\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/underlay/Underlay.tsx"], "sourcesContent": ["import React from 'react'\r\nimport styles from './Underlay.module.scss'\r\n\r\ninterface Props {\r\n  children: React.ReactNode\r\n  isOpen: boolean\r\n  onClose?: () => void\r\n  bgOpacity?: number\r\n  // zIndex?: number\r\n}\r\n\r\nconst Underlay = ({ children, isOpen, onClose, bgOpacity = 0.3 }: Props) => {\r\n\r\n  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {\r\n    if (e.target === e.currentTarget && onClose) {\r\n      onClose()\r\n    }\r\n  }\r\n\r\n  return isOpen ? (\r\n    <div\r\n      className={styles.underlay}\r\n      style={{ backgroundColor: `rgba(0, 0, 0, ${bgOpacity})` }}\r\n      onClick={handleOverlayClick}\r\n    >\r\n      {children}\r\n    </div>\r\n  ) : null\r\n}\r\n\r\nexport default Underlay"], "names": [], "mappings": ";;;;;AACA;;;AAUA,MAAM,WAAW,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,GAAG,EAAS;IAErE,MAAM,qBAAqB,CAAC;QAC1B,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,SAAS;YAC3C;QACF;IACF;IAEA,OAAO,uBACL,8OAAC;QACC,WAAW,0KAAM,CAAC,QAAQ;QAC1B,OAAO;YAAE,iBAAiB,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAAC;QACxD,SAAS;kBAER;;;;;mDAED;AACN;uCAEe", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/spinner/Spinner.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport SyncLoader from 'react-spinners/SyncLoader'\r\nimport <PERSON><PERSON><PERSON>oader from 'react-spinners/ClipLoader'\r\nimport PulseLoader from 'react-spinners/PulseLoader'\r\nimport RiseLoader from 'react-spinners/RiseLoader'\r\nimport RotateLoader from 'react-spinners/RotateLoader'\r\nimport ScaleLoader from 'react-spinners/ScaleLoader'\r\nimport CircleLoader from 'react-spinners/CircleLoader'\r\nimport Underlay from '../underlay/Underlay'\r\n\r\ninterface Props {\r\n  loading: boolean\r\n  color?: string\r\n  size?: number\r\n  thickness?: number\r\n  bgOpacity?: number\r\n  useUnderlay?: boolean\r\n  spinnerType?: 'sync' | 'pulse' | 'clip' | 'rise' | 'rotate' | 'scale' | 'circle'\r\n}\r\n\r\nconst Spinner = ({\r\n  loading,\r\n  color,\r\n  size = 150,\r\n  thickness = 5,\r\n  bgOpacity,\r\n  useUnderlay = false,\r\n  spinnerType = 'sync'\r\n}: Props) => {\r\n  const renderSpinner = () => {\r\n    const commonProps = {\r\n      color,\r\n      loading,\r\n      size,\r\n      thickness,\r\n      'aria-label': 'Loading Spinner',\r\n    }\r\n\r\n    switch (spinnerType) {\r\n      case 'clip':\r\n        return <ClipLoader {...commonProps} />\r\n      case 'pulse':\r\n        return <PulseLoader {...commonProps} />\r\n      case 'rise':\r\n        return <RiseLoader {...commonProps} />\r\n      case 'rotate':\r\n        return <RotateLoader {...commonProps} />\r\n      case 'scale':\r\n        return <ScaleLoader {...commonProps} />\r\n      case 'circle':\r\n        return <CircleLoader {...commonProps} />\r\n      case 'sync':\r\n      default:\r\n        return <SyncLoader {...commonProps} />\r\n    }\r\n  }\r\n\r\n  if (!useUnderlay) {\r\n    return loading ? renderSpinner() : null\r\n  }\r\n\r\n  return (\r\n    <Underlay isOpen={loading} bgOpacity={bgOpacity}>\r\n      {renderSpinner()}\r\n    </Underlay>\r\n  )\r\n}\r\n\r\nexport default Spinner"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAqBA,MAAM,UAAU,CAAC,EACf,OAAO,EACP,KAAK,EACL,OAAO,GAAG,EACV,YAAY,CAAC,EACb,SAAS,EACT,cAAc,KAAK,EACnB,cAAc,MAAM,EACd;IACN,MAAM,gBAAgB;QACpB,MAAM,cAAc;YAClB;YACA;YACA;YACA;YACA,cAAc;QAChB;QAEA,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,0JAAU;oBAAE,GAAG,WAAW;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,2JAAW;oBAAE,GAAG,WAAW;;;;;;YACrC,KAAK;gBACH,qBAAO,8OAAC,0JAAU;oBAAE,GAAG,WAAW;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,4JAAY;oBAAE,GAAG,WAAW;;;;;;YACtC,KAAK;gBACH,qBAAO,8OAAC,2JAAW;oBAAE,GAAG,WAAW;;;;;;YACrC,KAAK;gBACH,qBAAO,8OAAC,4JAAY;oBAAE,GAAG,WAAW;;;;;;YACtC,KAAK;YACL;gBACE,qBAAO,8OAAC,0JAAU;oBAAE,GAAG,WAAW;;;;;;QACtC;IACF;IAEA,IAAI,CAAC,aAAa;QAChB,OAAO,UAAU,kBAAkB;IACrC;IAEA,qBACE,8OAAC,8JAAQ;QAAC,QAAQ;QAAS,WAAW;kBACnC;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/button-state/ButtonState.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"loadingSpan\": \"ButtonState-module-scss-module__25_Fyq__loadingSpan\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/button-state/ButtonState.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Spinner from '@/src/components/utils/spinner/Spinner'\nimport styles from './ButtonState.module.scss'\n\ninterface ButtonStateProps {\n  isLoading: boolean\n  loadingText?: string\n  buttonText: string\n  spinnerSize?: number\n  spinnerType?: 'sync' | 'pulse' | 'clip' | 'rise' | 'rotate' | 'scale' | 'circle'\n  spinnerColor?: string\n  spinnerThickness?: number\n  children?: React.ReactNode\n}\n\nconst ButtonState: React.FC<ButtonStateProps> = ({\n  isLoading,\n  loadingText = 'Loading...',\n  buttonText,\n  spinnerSize = 20,\n  spinnerColor = '#ffffff',\n  spinnerType,\n  spinnerThickness = 2,\n}) => {\n  return (\n    <>\n      {isLoading ? (\n        <span className={styles.loadingSpan}>\n          <Spinner\n            loading={isLoading}\n            size={spinnerSize}\n            color={spinnerColor}\n            spinnerType={spinnerType}\n            thickness={spinnerThickness}\n          />\n          <span>{loadingText}</span>\n        </span>\n      ) : (\n        buttonText\n      )}\n    </>\n  )\n}\n\nexport default ButtonState"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAiBA,MAAM,cAA0C,CAAC,EAC/C,SAAS,EACT,cAAc,YAAY,EAC1B,UAAU,EACV,cAAc,EAAE,EAChB,eAAe,SAAS,EACxB,WAAW,EACX,mBAAmB,CAAC,EACrB;IACC,qBACE;kBACG,0BACC,8OAAC;YAAK,WAAW,oLAAM,CAAC,WAAW;;8BACjC,8OAAC,4JAAO;oBACN,SAAS;oBACT,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,WAAW;;;;;;8BAEb,8OAAC;8BAAM;;;;;;;;;;;uDAGT;;AAIR;uCAEe", "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/empty-cart/EmptyCart.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"action_button\": \"EmptyCart-module-scss-module__p5kxma__action_button\",\n  \"action_section\": \"EmptyCart-module-scss-module__p5kxma__action_section\",\n  \"content_section\": \"EmptyCart-module-scss-module__p5kxma__content_section\",\n  \"description\": \"EmptyCart-module-scss-module__p5kxma__description\",\n  \"empty_cart\": \"EmptyCart-module-scss-module__p5kxma__empty_cart\",\n  \"heading\": \"EmptyCart-module-scss-module__p5kxma__heading\",\n  \"icon_section\": \"EmptyCart-module-scss-module__p5kxma__icon_section\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/empty-cart/EmptyCart.tsx"], "sourcesContent": ["import Link from 'next/link'\r\nimport { FaShoppingCart } from 'react-icons/fa'\r\nimport styles from './EmptyCart.module.scss'\r\n\r\ninterface Props {\r\n  message?: string\r\n  showIcon?: boolean\r\n  actionText?: string\r\n  actionHref?: string\r\n}\r\n\r\nconst EmptyCart = ({\r\n  message,\r\n  showIcon = true,\r\n  actionText = \"Go Shopping\",\r\n  actionHref = \"/\"\r\n}: Props) => {\r\n  return (\r\n    <div className={styles.empty_cart} role=\"region\" aria-label=\"Empty cart\">\r\n      {showIcon && (\r\n        <div className={styles.icon_section} aria-hidden=\"true\">\r\n          <FaShoppingCart />\r\n        </div>\r\n      )}\r\n\r\n      <div className={styles.content_section}>\r\n        <h2 className={styles.heading}>\r\n          {message ? message : \"Your cart is empty\"}\r\n        </h2>\r\n        <p className={styles.description}>\r\n          Add some products to your cart to get started with your shopping journey.\r\n        </p>\r\n      </div>\r\n\r\n      <div className={styles.action_section}>\r\n        <Link\r\n          href={actionHref}\r\n          className={styles.action_button}\r\n          aria-label={`${actionText} - Browse products to add to your cart`}\r\n        >\r\n          {actionText}\r\n        </Link>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\nexport default EmptyCart"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AASA,MAAM,YAAY,CAAC,EACjB,OAAO,EACP,WAAW,IAAI,EACf,aAAa,aAAa,EAC1B,aAAa,GAAG,EACV;IACN,qBACE,8OAAC;QAAI,WAAW,gLAAM,CAAC,UAAU;QAAE,MAAK;QAAS,cAAW;;YACzD,0BACC,8OAAC;gBAAI,WAAW,gLAAM,CAAC,YAAY;gBAAE,eAAY;0BAC/C,cAAA,8OAAC,gKAAc;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAW,gLAAM,CAAC,eAAe;;kCACpC,8OAAC;wBAAG,WAAW,gLAAM,CAAC,OAAO;kCAC1B,UAAU,UAAU;;;;;;kCAEvB,8OAAC;wBAAE,WAAW,gLAAM,CAAC,WAAW;kCAAE;;;;;;;;;;;;0BAKpC,8OAAC;gBAAI,WAAW,gLAAM,CAAC,cAAc;0BACnC,cAAA,8OAAC,uKAAI;oBACH,MAAM;oBACN,WAAW,gLAAM,CAAC,aAAa;oBAC/B,cAAY,GAAG,WAAW,sCAAsC,CAAC;8BAEhE;;;;;;;;;;;;;;;;;AAKX;uCACe", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/components/layout/CheckoutPageWrapper.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { AxiosError } from 'axios'\nimport authStore from '@/src/stores/auth-store'\nimport cartStore from '@/src/stores/cart-store'\nimport { useCart } from '@/src/hooks/cart-hooks'\nimport EmptyCart from '@/src/components/utils/empty-cart/EmptyCart'\nimport Spinner from '@/src/components/utils/spinner/Spinner'\nimport Alert from '@/src/components/utils/alert/Alert'\nimport { getErrorMessage } from '@/src/components/utils/getErrorMessage'\nimport { ErrorResponse } from '@/src/types/types'\n\ninterface CheckoutPageWrapperProps {\n  children: ReactNode\n  requireAuth?: boolean\n  emptyCartMessage?: string\n  showCartValidation?: boolean\n}\n\nconst CheckoutPageWrapper = ({ \n  children, \n  requireAuth = true, \n  emptyCartMessage = 'Your cart is empty. Add some products to the cart to checkout!',\n  showCartValidation = true\n}: CheckoutPageWrapperProps) => {\n  const router = useRouter()\n  const { isLoggedIn } = authStore()\n  const { cartId } = cartStore()\n  const { isPending, error, data } = useCart()\n\n  // Handle authentication requirement\n  useEffect(() => {\n    if (requireAuth && !isLoggedIn) {\n      router.push('/login')\n    }\n  }, [isLoggedIn, router, requireAuth])\n\n  // Don't render anything if auth is required but user is not logged in\n  if (requireAuth && !isLoggedIn) {\n    return null\n  }\n\n  // Handle cart validation\n  if (showCartValidation) {\n    if (!cartId) {\n      return <EmptyCart message={emptyCartMessage} />\n    }\n\n    if (isPending) {\n      return <Spinner color='#0091CF' size={20} loading={true} />\n    }\n\n    if (error) {\n      return (\n        <Alert\n          variant='error'\n          message={getErrorMessage(error as AxiosError<ErrorResponse>)}\n        />\n      )\n    }\n\n    if (!data || data?.cart_items?.length === 0 || Object.keys(data).length === 0) {\n      return <EmptyCart message={emptyCartMessage} />\n    }\n  }\n\n  return <>{children}</>\n}\n\nexport default CheckoutPageWrapper\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAqBA,MAAM,sBAAsB,CAAC,EAC3B,QAAQ,EACR,cAAc,IAAI,EAClB,mBAAmB,gEAAgE,EACnF,qBAAqB,IAAI,EACA;IACzB,MAAM,SAAS,IAAA,+IAAS;IACxB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,yIAAS;IAChC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,yIAAS;IAC5B,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAA,wIAAO;IAE1C,oCAAoC;IACpC,IAAA,kNAAS,EAAC;QACR,IAAI,eAAe,CAAC,YAAY;YAC9B,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAY;QAAQ;KAAY;IAEpC,sEAAsE;IACtE,IAAI,eAAe,CAAC,YAAY;QAC9B,OAAO;IACT;IAEA,yBAAyB;IACzB,IAAI,oBAAoB;QACtB,IAAI,CAAC,QAAQ;YACX,qBAAO,8OAAC,oKAAS;gBAAC,SAAS;;;;;;QAC7B;QAEA,IAAI,WAAW;YACb,qBAAO,8OAAC,4JAAO;gBAAC,OAAM;gBAAU,MAAM;gBAAI,SAAS;;;;;;QACrD;QAEA,IAAI,OAAO;YACT,qBACE,8OAAC,wJAAK;gBACJ,SAAQ;gBACR,SAAS,IAAA,gKAAe,EAAC;;;;;;QAG/B;QAEA,IAAI,CAAC,QAAQ,MAAM,YAAY,WAAW,KAAK,OAAO,IAAI,CAAC,MAAM,MAAM,KAAK,GAAG;YAC7E,qBAAO,8OAAC,oKAAS;gBAAC,SAAS;;;;;;QAC7B;IACF;IAEA,qBAAO;kBAAG;;AACZ;uCAEe", "debugId": null}}, {"offset": {"line": 619, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/components/layout/ContactDetailsSection.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"contact_details\": \"ContactDetailsSection-module-scss-module__q_i1eq__contact_details\",\n  \"contact_info\": \"ContactDetailsSection-module-scss-module__q_i1eq__contact_info\",\n  \"shipping_address\": \"ContactDetailsSection-module-scss-module__q_i1eq__shipping_address\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 628, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/components/layout/ContactDetailsSection.tsx"], "sourcesContent": ["import { CustomerShape } from '@/src/types/store-types'\nimport { AddressFormInputs } from '@/src/components/account/addresses/ManageAddresses'\nimport styles from './ContactDetailsSection.module.scss'\n\ninterface ContactDetailsSectionProps {\n  customer: CustomerShape | undefined\n  selectedAddress?: AddressFormInputs\n  showShippingAddress?: boolean\n}\n\nconst ContactDetailsSection = ({\n  customer,\n  selectedAddress,\n  showShippingAddress = false,\n}: ContactDetailsSectionProps) => {\n  return (\n    <section className={styles.contact_info}>\n      <div className={styles.contact_details}>\n        <h3>Contact Details: </h3>\n        <p>\n          Deliver to: {customer?.first_name} {customer?.last_name}\n        </p>\n        <p>Phone: {customer?.phone_number}</p>\n        <p>Email to: {customer?.email}</p>\n      </div>\n\n      {showShippingAddress && selectedAddress && (\n        <div className={styles.shipping_address}>\n          <h3>Shipping address: </h3>\n          <address>\n            {selectedAddress.full_name},<br />\n            {selectedAddress.street_name},<br />\n            {selectedAddress.postal_code},<br />\n            {selectedAddress.city_or_village}\n            <br />\n          </address>\n        </div>\n      )}\n    </section>\n  )\n}\n\nexport default ContactDetailsSection\n"], "names": [], "mappings": ";;;;;AAEA;;;AAQA,MAAM,wBAAwB,CAAC,EAC7B,QAAQ,EACR,eAAe,EACf,sBAAsB,KAAK,EACA;IAC3B,qBACE,8OAAC;QAAQ,WAAW,mNAAM,CAAC,YAAY;;0BACrC,8OAAC;gBAAI,WAAW,mNAAM,CAAC,eAAe;;kCACpC,8OAAC;kCAAG;;;;;;kCACJ,8OAAC;;4BAAE;4BACY,UAAU;4BAAW;4BAAE,UAAU;;;;;;;kCAEhD,8OAAC;;4BAAE;4BAAQ,UAAU;;;;;;;kCACrB,8OAAC;;4BAAE;4BAAW,UAAU;;;;;;;;;;;;;YAGzB,uBAAuB,iCACtB,8OAAC;gBAAI,WAAW,mNAAM,CAAC,gBAAgB;;kCACrC,8OAAC;kCAAG;;;;;;kCACJ,8OAAC;;4BACE,gBAAgB,SAAS;4BAAC;0CAAC,8OAAC;;;;;4BAC5B,gBAAgB,WAAW;4BAAC;0CAAC,8OAAC;;;;;4BAC9B,gBAAgB,WAAW;4BAAC;0CAAC,8OAAC;;;;;4BAC9B,gBAAgB,eAAe;0CAChC,8OAAC;;;;;;;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/TextLimit.tsx"], "sourcesContent": ["interface Props {\r\n  title: string\r\n  maxLength: number\r\n}\r\n\r\nconst LimitTitleLength = ({ title, maxLength }: Props) => {\r\n  function limitTitleLength(title: string, maxLength: number) {\r\n    if (title.length > maxLength) {\r\n      return title.substring(0, maxLength) + '...'\r\n    }\r\n    return title\r\n  }\r\n\r\n  return <>{limitTitleLength(title, maxLength)}</>\r\n}\r\n\r\nexport default LimitTitleLength"], "names": [], "mappings": ";;;;;;AAKA,MAAM,mBAAmB,CAAC,EAAE,KAAK,EAAE,SAAS,EAAS;IACnD,SAAS,iBAAiB,KAAa,EAAE,SAAiB;QACxD,IAAI,MAAM,MAAM,GAAG,WAAW;YAC5B,OAAO,MAAM,SAAS,CAAC,GAAG,aAAa;QACzC;QACA,OAAO;IACT;IAEA,qBAAO;kBAAG,iBAAiB,OAAO;;AACpC;uCAEe", "debugId": null}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cart_item\": \"CartItemsList-module-scss-module__kultEW__cart_item\",\n  \"cart_item__extra_data\": \"CartItemsList-module-scss-module__kultEW__cart_item__extra_data\",\n  \"cart_item__img\": \"CartItemsList-module-scss-module__kultEW__cart_item__img\",\n  \"cart_item__info\": \"CartItemsList-module-scss-module__kultEW__cart_item__info\",\n  \"cart_item__quantity\": \"CartItemsList-module-scss-module__kultEW__cart_item__quantity\",\n  \"cart_item__title\": \"CartItemsList-module-scss-module__kultEW__cart_item__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/components/cart/CartItemsList.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { <PERSON><PERSON><PERSON>, <PERSON>Minus, FiTrash2 } from 'react-icons/fi'\r\nimport Link from 'next/link'\r\nimport Image from 'next/image'\r\nimport LimitTitleLength from '@/src/components/utils/TextLimit'\r\nimport { CartItemShape } from '@/src/types/store-types'\r\nimport styles from './CartItemsList.module.scss'\r\n\r\ninterface CartItemsListProps {\r\n  cartItems: CartItemShape[]\r\n  handleIncrement?: (item: CartItemShape) => void\r\n  handleDecrement?: (item: CartItemShape) => void\r\n  deleteCartItem?: (itemId: number) => void\r\n}\r\n\r\nconst CartItemsList = ({\r\n  cartItems,\r\n  handleIncrement,\r\n  handleDecrement,\r\n  deleteCartItem,\r\n}: CartItemsListProps) => {\r\n  return (\r\n    <ul>\r\n      {cartItems?.map((item: CartItemShape) => (\r\n        <li key={item.id} className={styles.cart_item}>\r\n          <div className={styles.cart_item__img}>\r\n            <Image\r\n              src={\r\n                item.product_variant?.product_image?.[0]?.image\r\n                  ? `${process.env.NEXT_PUBLIC_CLOUDINARY_URL}/${item.product_variant.product_image[0].image}`\r\n                  : '/images/no-image-placeholder.png'\r\n              }\r\n              alt={\r\n                item.product_variant?.product_image?.[0]?.alternative_text ||\r\n                item.product.title\r\n              }\r\n              width={80}\r\n              height={80}\r\n              style={{ objectFit: 'cover' }}\r\n            />\r\n          </div>\r\n          <div className={styles.cart_item__info}>\r\n            <span className={styles.cart_item__title}>\r\n              <Link href={`/product/${item.product.slug}/`}>\r\n                <LimitTitleLength title={item.product.title} maxLength={60} />\r\n              </Link>\r\n            </span>\r\n            {` `}\r\n            <span>\r\n              (${item.product_variant.price} x {item.quantity})\r\n            </span>\r\n            <span>\r\n              variant: {item.product_variant?.price_label?.attribute_value}\r\n            </span>\r\n            {Object.entries(item.extra_data).map(([key, value], index) => (\r\n              <div key={index} className={styles.cart_item__extra_data}>\r\n                <p>{key} :</p>\r\n                <p>{value}</p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n          <div className={styles.cart_item__quantity}>\r\n            <div>\r\n              <p>Qty:</p>\r\n              {handleDecrement && (\r\n                <button\r\n                  onClick={() => handleDecrement(item)}\r\n                  disabled={item.product_variant.stock_qty === 0}\r\n                >\r\n                  <i>\r\n                    <FiMinus />\r\n                  </i>\r\n                </button>\r\n              )}\r\n              <p>{item.quantity}</p>\r\n              {handleIncrement && (\r\n                <button\r\n                  onClick={() => handleIncrement(item)}\r\n                  disabled={item.product_variant.stock_qty === 0}\r\n                >\r\n                  <i>\r\n                    <FiPlus />\r\n                  </i>\r\n                </button>\r\n              )}\r\n              {deleteCartItem && (\r\n                <button onClick={() => deleteCartItem(item.id)}>\r\n                  <i>\r\n                    <FiTrash2 />\r\n                  </i>\r\n                </button>\r\n              )}\r\n            </div>\r\n            {item.product_variant.stock_qty === 0 && <p>Out of Stock</p>}\r\n          </div>\r\n        </li>\r\n      ))}\r\n    </ul>\r\n  )\r\n}\r\n\r\nexport default CartItemsList\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AAgBA,MAAM,gBAAgB,CAAC,EACrB,SAAS,EACT,eAAe,EACf,eAAe,EACf,cAAc,EACK;IACnB,qBACE,8OAAC;kBACE,WAAW,IAAI,CAAC,qBACf,8OAAC;gBAAiB,WAAW,yMAAM,CAAC,SAAS;;kCAC3C,8OAAC;wBAAI,WAAW,yMAAM,CAAC,cAAc;kCACnC,cAAA,8OAAC,wIAAK;4BACJ,KACE,KAAK,eAAe,EAAE,eAAe,CAAC,EAAE,EAAE,QACtC,yEAA0C,CAAC,EAAE,KAAK,eAAe,CAAC,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,GAC1F;4BAEN,KACE,KAAK,eAAe,EAAE,eAAe,CAAC,EAAE,EAAE,oBAC1C,KAAK,OAAO,CAAC,KAAK;4BAEpB,OAAO;4BACP,QAAQ;4BACR,OAAO;gCAAE,WAAW;4BAAQ;;;;;;;;;;;kCAGhC,8OAAC;wBAAI,WAAW,yMAAM,CAAC,eAAe;;0CACpC,8OAAC;gCAAK,WAAW,yMAAM,CAAC,gBAAgB;0CACtC,cAAA,8OAAC,uKAAI;oCAAC,MAAM,CAAC,SAAS,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;8CAC1C,cAAA,8OAAC,mJAAgB;wCAAC,OAAO,KAAK,OAAO,CAAC,KAAK;wCAAE,WAAW;;;;;;;;;;;;;;;;4BAG3D,CAAC,CAAC,CAAC;0CACJ,8OAAC;;oCAAK;oCACD,KAAK,eAAe,CAAC,KAAK;oCAAC;oCAAI,KAAK,QAAQ;oCAAC;;;;;;;0CAElD,8OAAC;;oCAAK;oCACM,KAAK,eAAe,EAAE,aAAa;;;;;;;4BAE9C,OAAO,OAAO,CAAC,KAAK,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE,sBAClD,8OAAC;oCAAgB,WAAW,yMAAM,CAAC,qBAAqB;;sDACtD,8OAAC;;gDAAG;gDAAI;;;;;;;sDACR,8OAAC;sDAAG;;;;;;;mCAFI;;;;;;;;;;;kCAMd,8OAAC;wBAAI,WAAW,yMAAM,CAAC,mBAAmB;;0CACxC,8OAAC;;kDACC,8OAAC;kDAAE;;;;;;oCACF,iCACC,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,UAAU,KAAK,eAAe,CAAC,SAAS,KAAK;kDAE7C,cAAA,8OAAC;sDACC,cAAA,8OAAC,yJAAO;;;;;;;;;;;;;;;kDAId,8OAAC;kDAAG,KAAK,QAAQ;;;;;;oCAChB,iCACC,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,UAAU,KAAK,eAAe,CAAC,SAAS,KAAK;kDAE7C,cAAA,8OAAC;sDACC,cAAA,8OAAC,wJAAM;;;;;;;;;;;;;;;oCAIZ,gCACC,8OAAC;wCAAO,SAAS,IAAM,eAAe,KAAK,EAAE;kDAC3C,cAAA,8OAAC;sDACC,cAAA,8OAAC,0JAAQ;;;;;;;;;;;;;;;;;;;;;4BAKhB,KAAK,eAAe,CAAC,SAAS,KAAK,mBAAK,8OAAC;0CAAE;;;;;;;;;;;;;eArEvC,KAAK,EAAE;;;;;;;;;;AA2ExB;uCAEe", "debugId": null}}, {"offset": {"line": 1017, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cart__checkout\": \"PriceSummary-module-scss-module__7p8iVa__cart__checkout\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 1023, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/utils/tooltip/Tooltip.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"tooltip\": \"Tooltip-module-scss-module__vdbe-W__tooltip\",\n  \"tooltip--bottom\": \"Tooltip-module-scss-module__vdbe-W__tooltip--bottom\",\n  \"tooltip--condition\": \"Tooltip-module-scss-module__vdbe-W__tooltip--condition\",\n  \"tooltip--hover\": \"Tooltip-module-scss-module__vdbe-W__tooltip--hover\",\n  \"tooltip--left\": \"Tooltip-module-scss-module__vdbe-W__tooltip--left\",\n  \"tooltip--right\": \"Tooltip-module-scss-module__vdbe-W__tooltip--right\",\n  \"tooltip--top\": \"Tooltip-module-scss-module__vdbe-W__tooltip--top\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 1036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/components/utils/tooltip/Tooltip.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react'\nimport styles from './Tooltip.module.scss'\n\ninterface TooltipProps {\n  children: ReactNode\n  content: string\n  position?: 'top' | 'bottom' | 'left' | 'right'\n  disabled?: boolean\n  className?: string\n  showOnHover?: boolean\n  showOnCondition?: boolean\n}\n\nconst Tooltip: React.FC<TooltipProps> = ({\n  children,\n  content,\n  position = 'top',\n  disabled = false,\n  className = '',\n  showOnHover = true,\n  showOnCondition = false\n}) => {\n  // Don't show tooltip if disabled or no content\n  if (disabled || !content) {\n    return <>{children}</>\n  }\n\n  const tooltipClasses = [\n    styles.tooltip,\n    styles[`tooltip--${position}`],\n    showOnHover ? styles['tooltip--hover'] : '',\n    showOnCondition ? styles['tooltip--condition'] : '',\n    className\n  ].filter(Boolean).join(' ')\n\n  return (\n    <div className={tooltipClasses} data-tooltip={content}>\n      {children}\n    </div>\n  )\n}\n\nexport default Tooltip\n"], "names": [], "mappings": ";;;;;AACA;;;AAYA,MAAM,UAAkC,CAAC,EACvC,QAAQ,EACR,OAAO,EACP,WAAW,KAAK,EAChB,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,cAAc,IAAI,EAClB,kBAAkB,KAAK,EACxB;IACC,+CAA+C;IAC/C,IAAI,YAAY,CAAC,SAAS;QACxB,qBAAO;sBAAG;;IACZ;IAEA,MAAM,iBAAiB;QACrB,wKAAM,CAAC,OAAO;QACd,wKAAM,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC;QAC9B,cAAc,wKAAM,CAAC,iBAAiB,GAAG;QACzC,kBAAkB,wKAAM,CAAC,qBAAqB,GAAG;QACjD;KACD,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvB,qBACE,8OAAC;QAAI,WAAW;QAAgB,gBAAc;kBAC3C;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 1073, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/components/price-summary/PriceSummary.tsx"], "sourcesContent": ["import React from 'react'\r\nimport { RiQuestionFill } from 'react-icons/ri'\r\nimport styles from './PriceSummary.module.scss'\r\nimport Tooltip from '@/src/components/utils/tooltip/Tooltip'\r\n\r\ninterface PriceSummaryProps {\r\n  totalPrice: number\r\n  // shippingCost: number\r\n  // grandTotal: number\r\n  item_count: number\r\n  cart_weight: number\r\n  onCheckout?: () => void\r\n}\r\n\r\nconst PriceSummary: React.FC<PriceSummaryProps> = ({\r\n  totalPrice,\r\n  item_count,\r\n  cart_weight,\r\n  onCheckout,\r\n}) => {\r\n  return (\r\n    <div className={styles.cart__checkout}>\r\n      <div>\r\n        {/* <p>Total: </p> <p>${grandTotal.toFixed(2)}</p> */}\r\n        <p>Item count: </p> <p>{item_count}</p>\r\n      </div>\r\n      <div>\r\n        {/* <p>Shipping cost:\r\n          <Tooltip\r\n            content={`Cost for packaging & weight of cart items (${cart_weight}g)`}\r\n            position=\"top\"\r\n          >\r\n            <i><RiQuestionFill /></i>\r\n          </Tooltip>\r\n        </p>\r\n        <p>${shippingCost.toFixed(2)}</p> */}\r\n\r\n        <p>\r\n          Cart weight:\r\n          <Tooltip\r\n            content={`Shipping cost will be calculated after finalizing adding items to the cart.`}\r\n            position='top'\r\n          >\r\n            <i>\r\n              <RiQuestionFill />\r\n            </i>\r\n          </Tooltip>\r\n        </p>\r\n        <p>{cart_weight}g</p>\r\n\r\n        {/* <p>Cart weight: </p> <p>{cart_weight}g</p> */}\r\n      </div>\r\n\r\n      <div>\r\n        <p>Item&apos;s cost: </p> <p>${totalPrice.toFixed(2)}</p>\r\n      </div>\r\n      {onCheckout && <button onClick={onCheckout}>Proceed to checkout</button>}\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default PriceSummary\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAWA,MAAM,eAA4C,CAAC,EACjD,UAAU,EACV,UAAU,EACV,WAAW,EACX,UAAU,EACX;IACC,qBACE,8OAAC;QAAI,WAAW,oNAAM,CAAC,cAAc;;0BACnC,8OAAC;;kCAEC,8OAAC;kCAAE;;;;;;oBAAgB;kCAAC,8OAAC;kCAAG;;;;;;;;;;;;0BAE1B,8OAAC;;kCAWC,8OAAC;;4BAAE;0CAED,8OAAC,4JAAO;gCACN,SAAS,CAAC,2EAA2E,CAAC;gCACtF,UAAS;0CAET,cAAA,8OAAC;8CACC,cAAA,8OAAC,gKAAc;;;;;;;;;;;;;;;;;;;;;kCAIrB,8OAAC;;4BAAG;4BAAY;;;;;;;;;;;;;0BAKlB,8OAAC;;kCACC,8OAAC;kCAAE;;;;;;oBAAsB;kCAAC,8OAAC;;4BAAE;4BAAE,WAAW,OAAO,CAAC;;;;;;;;;;;;;YAEnD,4BAAc,8OAAC;gBAAO,SAAS;0BAAY;;;;;;;;;;;;AAGlD;uCAEe", "debugId": null}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/(shop)/(checkout-process)/components/layout/CartDisplaySection.module.scss [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cart_display\": \"CartDisplaySection-module-scss-module__7Aiwqa__cart_display\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA"}}, {"offset": {"line": 1210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/components/layout/CartDisplaySection.tsx"], "sourcesContent": ["import { CartItemShape } from '@/src/types/store-types'\nimport CartItemsList from '../cart/CartItemsList'\nimport PriceSummary from '../price-summary/PriceSummary'\nimport styles from './CartDisplaySection.module.scss'\n\ninterface CartDisplaySectionProps {\n  cartItems: CartItemShape[]\n  totalPrice: number\n  itemCount: number\n  cartWeight: number\n  handleIncrement?: (item: CartItemShape) => void\n  handleDecrement?: (item: CartItemShape) => void\n  deleteCartItem?: (itemId: number) => void\n  onCheckout?: () => void\n  showCheckoutButton?: boolean\n}\n\nconst CartDisplaySection = ({\n  cartItems,\n  totalPrice,\n  itemCount,\n  cartWeight,\n  handleIncrement,\n  handleDecrement,\n  deleteCartItem,\n  onCheckout,\n  showCheckoutButton = false\n}: CartDisplaySectionProps) => {\n  return (\n    <div className={styles.cart_display}>\n      <CartItemsList\n        cartItems={cartItems}\n        handleIncrement={handleIncrement}\n        handleDecrement={handleDecrement}\n        deleteCartItem={deleteCartItem}\n      />\n      <PriceSummary\n        totalPrice={totalPrice}\n        item_count={itemCount}\n        cart_weight={cartWeight}\n        onCheckout={showCheckoutButton ? onCheckout : undefined}\n      />\n    </div>\n  )\n}\n\nexport default CartDisplaySection\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;;AAcA,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,eAAe,EACf,eAAe,EACf,cAAc,EACd,UAAU,EACV,qBAAqB,KAAK,EACF;IACxB,qBACE,8OAAC;QAAI,WAAW,gNAAM,CAAC,YAAY;;0BACjC,8OAAC,6LAAa;gBACZ,WAAW;gBACX,iBAAiB;gBACjB,iBAAiB;gBACjB,gBAAgB;;;;;;0BAElB,8OAAC,wMAAY;gBACX,YAAY;gBACZ,YAAY;gBACZ,aAAa;gBACb,YAAY,qBAAqB,aAAa;;;;;;;;;;;;AAItD;uCAEe", "debugId": null}}, {"offset": {"line": 1258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Projects/Projects%20Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/%28shop%29/%28checkout-process%29/checkout/payment-choice/page.tsx"], "sourcesContent": ["'use client'\n\nimport Alert from '@/src/components/utils/alert/Alert'\nimport { useCart } from '@/src/hooks/cart-hooks'\nimport { usePaymentOptions } from '@/src/hooks/checkout-hooks'\nimport { useCustomerDetails } from '@/src/hooks/customer-hooks'\nimport { useCreateOrder } from '@/src/hooks/order-hooks'\nimport authStore from '@/src/stores/auth-store'\nimport cartStore from '@/src/stores/cart-store'\nimport { PaymentOptionsShape } from '@/src/types/types'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport styles from './PaymentChoice.module.scss'\nimport ButtonState from '@/src/components/utils/button-state/ButtonState'\nimport CheckoutPageWrapper from '../../components/layout/CheckoutPageWrapper'\nimport ContactDetailsSection from '../../components/layout/ContactDetailsSection'\nimport CartDisplaySection from '../../components/layout/CartDisplaySection'\n\nconst PaymentChoice = () => {\n  const router = useRouter()\n  const { data: customer } = useCustomerDetails()\n  const {\n    cartId,\n    setSelectedPaymentOption,\n    selectedAddress,\n    selectedPaymentOption,\n  } = cartStore()\n  const { data } = useCart()\n\n  const { createOrder } = useCreateOrder()\n  const payOptions = usePaymentOptions()\n\n  // FOR TESTING UI ONLY: Force isPending to true to see loading spinner\n  // createOrder.isPending = true\n\n  console.log(payOptions.data)\n\n  const handlePaymentOptionChange = (paymentOption: PaymentOptionsShape) => {\n    setSelectedPaymentOption(paymentOption)\n  }\n\n  useEffect(() => {\n    if (\n      payOptions.data &&\n      payOptions.data.length > 0 &&\n      !selectedPaymentOption\n    ) {\n      setSelectedPaymentOption(payOptions.data[0])\n    }\n  }, [payOptions.data, selectedPaymentOption, setSelectedPaymentOption])\n\n  console.log(customer)\n  console.log(selectedAddress)\n  console.log(selectedPaymentOption)\n\n  const createOrderFn = () => {\n    if (\n      window.confirm(\n        'Payment Method or other order details cannot be changed after placing the order.\\n' +\n          'Make sure you have selected the correct payment method and other order details before placing the order.\\n' +\n          'Click OK to place the order or Cancel to go back and make changes.'\n      )\n    ) {\n      if (customer?.id && selectedAddress?.id && selectedPaymentOption?.id) {\n        createOrder.mutate(\n          {\n            cart_id: cartId!,\n            delivery_status: 'Pending',\n            selected_address: selectedAddress.id,\n            payment_method: selectedPaymentOption.id,\n          },\n          {\n            onSuccess: (data) => {\n              router.push(`/checkout/order/${data.id}`)\n            },\n          }\n        )\n      }\n    }\n  }\n\n  return (\n    <CheckoutPageWrapper>\n      {data && (\n        <div className='container'>\n          <h3 className={styles.place_order}>Place Order</h3>\n          <div className={styles.payment_options_stage}>\n            <section>\n              <ContactDetailsSection\n                customer={customer}\n                selectedAddress={selectedAddress}\n                showShippingAddress={true}\n              />\n              <hr />\n              <div className={styles.cart}>\n                <CartDisplaySection\n                  cartItems={data.cart_items}\n                  totalPrice={data.total_price}\n                  itemCount={data.item_count}\n                  cartWeight={data.cart_weight}\n                />\n              </div>\n              <hr />\n              <section>\n                <div className={styles.payment_options}>\n                  <h3>Payment Method:</h3>\n                  {payOptions?.isPending ? (\n                    <Alert\n                      variant='info'\n                      message='Payment options are loading'\n                    />\n                  ) : payOptions?.error ? (\n                    <Alert variant='error' message={payOptions.error.message} />\n                  ) : payOptions?.data?.length === 0 ? (\n                    <Alert\n                      variant='error'\n                      message='No payment options available'\n                    />\n                  ) : (\n                    <>\n                      {payOptions?.data?.map((option: PaymentOptionsShape) => (\n                        <div key={option.id}>\n                          <input\n                            type='radio'\n                            id={`payment-${option.id}`}\n                            name='payment-option'\n                            checked={selectedPaymentOption?.id === option.id}\n                            onChange={() => handlePaymentOptionChange(option)}\n                          />\n                          <label htmlFor={`payment-${option.id}`}>\n                            {option.name}\n                          </label>\n                        </div>\n                      ))}\n                    </>\n                  )}\n                </div>\n              </section>\n            </section>\n            <section className={styles.price_summary}>\n              <button\n                type='submit'\n                disabled={createOrder.isPending}\n                onClick={createOrderFn}\n              >\n                <ButtonState\n                  isLoading={createOrder.isPending}\n                  loadingText='Placing the Order...'\n                  buttonText='Place Order'\n                  spinnerSize={16}\n                  spinnerColor='#fff'\n                  spinnerType='clip'\n                />\n              </button>\n            </section>\n          </div>\n        </div>\n      )}\n    </CheckoutPageWrapper>\n  )\n}\n\nexport default PaymentChoice\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;AAkBA,MAAM,gBAAgB;IACpB,MAAM,SAAS,IAAA,+IAAS;IACxB,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,IAAA,uJAAkB;IAC7C,MAAM,EACJ,MAAM,EACN,wBAAwB,EACxB,eAAe,EACf,qBAAqB,EACtB,GAAG,IAAA,yIAAS;IACb,MAAM,EAAE,IAAI,EAAE,GAAG,IAAA,wIAAO;IAExB,MAAM,EAAE,WAAW,EAAE,GAAG,IAAA,gJAAc;IACtC,MAAM,aAAa,IAAA,sJAAiB;IAEpC,sEAAsE;IACtE,+BAA+B;IAE/B,QAAQ,GAAG,CAAC,WAAW,IAAI;IAE3B,MAAM,4BAA4B,CAAC;QACjC,yBAAyB;IAC3B;IAEA,IAAA,kNAAS,EAAC;QACR,IACE,WAAW,IAAI,IACf,WAAW,IAAI,CAAC,MAAM,GAAG,KACzB,CAAC,uBACD;YACA,yBAAyB,WAAW,IAAI,CAAC,EAAE;QAC7C;IACF,GAAG;QAAC,WAAW,IAAI;QAAE;QAAuB;KAAyB;IAErE,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC;IAEZ,MAAM,gBAAgB;QACpB,IACE,OAAO,OAAO,CACZ,uFACE,+GACA,uEAEJ;YACA,IAAI,UAAU,MAAM,iBAAiB,MAAM,uBAAuB,IAAI;gBACpE,YAAY,MAAM,CAChB;oBACE,SAAS;oBACT,iBAAiB;oBACjB,kBAAkB,gBAAgB,EAAE;oBACpC,gBAAgB,sBAAsB,EAAE;gBAC1C,GACA;oBACE,WAAW,CAAC;wBACV,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,EAAE,EAAE;oBAC1C;gBACF;YAEJ;QACF;IACF;IAEA,qBACE,8OAAC,qMAAmB;kBACjB,sBACC,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAW,oNAAM,CAAC,WAAW;8BAAE;;;;;;8BACnC,8OAAC;oBAAI,WAAW,oNAAM,CAAC,qBAAqB;;sCAC1C,8OAAC;;8CACC,8OAAC,uMAAqB;oCACpB,UAAU;oCACV,iBAAiB;oCACjB,qBAAqB;;;;;;8CAEvB,8OAAC;;;;;8CACD,8OAAC;oCAAI,WAAW,oNAAM,CAAC,IAAI;8CACzB,cAAA,8OAAC,oMAAkB;wCACjB,WAAW,KAAK,UAAU;wCAC1B,YAAY,KAAK,WAAW;wCAC5B,WAAW,KAAK,UAAU;wCAC1B,YAAY,KAAK,WAAW;;;;;;;;;;;8CAGhC,8OAAC;;;;;8CACD,8OAAC;8CACC,cAAA,8OAAC;wCAAI,WAAW,oNAAM,CAAC,eAAe;;0DACpC,8OAAC;0DAAG;;;;;;4CACH,YAAY,0BACX,8OAAC,wJAAK;gDACJ,SAAQ;gDACR,SAAQ;;;;;2FAER,YAAY,sBACd,8OAAC,wJAAK;gDAAC,SAAQ;gDAAQ,SAAS,WAAW,KAAK,CAAC,OAAO;;;;;2FACtD,YAAY,MAAM,WAAW,kBAC/B,8OAAC,wJAAK;gDACJ,SAAQ;gDACR,SAAQ;;;;;yGAGV;0DACG,YAAY,MAAM,IAAI,CAAC,uBACtB,8OAAC;;0EACC,8OAAC;gEACC,MAAK;gEACL,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE;gEAC1B,MAAK;gEACL,SAAS,uBAAuB,OAAO,OAAO,EAAE;gEAChD,UAAU,IAAM,0BAA0B;;;;;;0EAE5C,8OAAC;gEAAM,SAAS,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE;0EACnC,OAAO,IAAI;;;;;;;uDATN,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAkB/B,8OAAC;4BAAQ,WAAW,oNAAM,CAAC,aAAa;sCACtC,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU,YAAY,SAAS;gCAC/B,SAAS;0CAET,cAAA,8OAAC,wKAAW;oCACV,WAAW,YAAY,SAAS;oCAChC,aAAY;oCACZ,YAAW;oCACX,aAAa;oCACb,cAAa;oCACb,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9B;uCAEe", "debugId": null}}]}