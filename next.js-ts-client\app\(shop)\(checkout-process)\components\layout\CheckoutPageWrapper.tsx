'use client'

import { ReactNode, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AxiosError } from 'axios'
import authStore from '@/src/stores/auth-store'
import cartStore from '@/src/stores/cart-store'
import { useCart } from '@/src/hooks/cart-hooks'
import EmptyCart from '@/src/components/utils/empty-cart/EmptyCart'
import Spinner from '@/src/components/utils/spinner/Spinner'
import Alert from '@/src/components/utils/alert/Alert'
import { getErrorMessage } from '@/src/components/utils/getErrorMessage'
import { ErrorResponse } from '@/src/types/types'

interface CheckoutPageWrapperProps {
  children: ReactNode
  requireAuth?: boolean
  emptyCartMessage?: string
  showCartValidation?: boolean
}

const CheckoutPageWrapper = ({ 
  children, 
  requireAuth = true, 
  emptyCartMessage = 'Your cart is empty. Add some products to the cart to checkout!',
  showCartValidation = true
}: CheckoutPageWrapperProps) => {
  const router = useRouter()
  const { isLoggedIn } = authStore()
  const { cartId } = cartStore()
  const { isPending, error, data } = useCart()

  // Handle authentication requirement
  useEffect(() => {
    if (requireAuth && !isLoggedIn) {
      router.push('/login')
    }
  }, [isLoggedIn, router, requireAuth])

  // Don't render anything if auth is required but user is not logged in
  if (requireAuth && !isLoggedIn) {
    return null
  }

  // Handle cart validation
  if (showCartValidation) {
    if (!cartId) {
      return <EmptyCart message={emptyCartMessage} />
    }

    if (isPending) {
      return <Spinner color='#0091CF' size={20} loading={true} />
    }

    if (error) {
      return (
        <Alert
          variant='error'
          message={getErrorMessage(error as AxiosError<ErrorResponse>)}
        />
      )
    }

    if (!data || data?.cart_items?.length === 0 || Object.keys(data).length === 0) {
      return <EmptyCart message={emptyCartMessage} />
    }
  }

  return <>{children}</>
}

export default CheckoutPageWrapper
