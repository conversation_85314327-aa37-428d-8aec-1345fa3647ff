'use client'

import Alert from '@/src/components/utils/alert/Alert'
import { useCart } from '@/src/hooks/cart-hooks'
import { usePaymentOptions } from '@/src/hooks/checkout-hooks'
import { useCustomerDetails } from '@/src/hooks/customer-hooks'
import { useCreateOrder } from '@/src/hooks/order-hooks'
import authStore from '@/src/stores/auth-store'
import cartStore from '@/src/stores/cart-store'
import { PaymentOptionsShape } from '@/src/types/types'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import styles from './PaymentChoice.module.scss'
import ButtonState from '@/src/components/utils/button-state/ButtonState'
import CheckoutPageWrapper from '../../components/layout/CheckoutPageWrapper'
import ContactDetailsSection from '../../components/layout/ContactDetailsSection'
import CartDisplaySection from '../../components/layout/CartDisplaySection'

const PaymentChoice = () => {
  const router = useRouter()
  const { data: customer } = useCustomerDetails()
  const {
    cartId,
    setSelectedPaymentOption,
    selectedAddress,
    selectedPaymentOption,
  } = cartStore()
  const { data } = useCart()

  const { createOrder } = useCreateOrder()
  const payOptions = usePaymentOptions()

  // FOR TESTING UI ONLY: Force isPending to true to see loading spinner
  // createOrder.isPending = true

  console.log(payOptions.data)

  const handlePaymentOptionChange = (paymentOption: PaymentOptionsShape) => {
    setSelectedPaymentOption(paymentOption)
  }

  useEffect(() => {
    if (
      payOptions.data &&
      payOptions.data.length > 0 &&
      !selectedPaymentOption
    ) {
      setSelectedPaymentOption(payOptions.data[0])
    }
  }, [payOptions.data, selectedPaymentOption, setSelectedPaymentOption])

  console.log(customer)
  console.log(selectedAddress)
  console.log(selectedPaymentOption)

  const createOrderFn = () => {
    if (
      window.confirm(
        'Payment Method or other order details cannot be changed after placing the order.\n' +
          'Make sure you have selected the correct payment method and other order details before placing the order.\n' +
          'Click OK to place the order or Cancel to go back and make changes.'
      )
    ) {
      if (customer?.id && selectedAddress?.id && selectedPaymentOption?.id) {
        createOrder.mutate(
          {
            cart_id: cartId!,
            delivery_status: 'Pending',
            selected_address: selectedAddress.id,
            payment_method: selectedPaymentOption.id,
          },
          {
            onSuccess: (data) => {
              router.push(`/checkout/order/${data.id}`)
            },
          }
        )
      }
    }
  }

  return (
    <CheckoutPageWrapper>
      {data && (
        <div className='container'>
          <h3 className={styles.place_order}>Place Order</h3>
          <div className={styles.payment_options_stage}>
            <section>
              <ContactDetailsSection
                customer={customer}
                selectedAddress={selectedAddress}
                showShippingAddress={true}
              />
              <hr />
              <div className={styles.cart}>
                <CartDisplaySection
                  cartItems={data.cart_items}
                  totalPrice={data.total_price}
                  itemCount={data.item_count}
                  cartWeight={data.cart_weight}
                />
              </div>
              <hr />
              <section>
                <div className={styles.payment_options}>
                  <h3>Payment Method:</h3>
                  {payOptions?.isPending ? (
                    <Alert
                      variant='info'
                      message='Payment options are loading'
                    />
                  ) : payOptions?.error ? (
                    <Alert variant='error' message={payOptions.error.message} />
                  ) : payOptions?.data?.length === 0 ? (
                    <Alert
                      variant='error'
                      message='No payment options available'
                    />
                  ) : (
                    <>
                      {payOptions?.data?.map((option: PaymentOptionsShape) => (
                        <div key={option.id}>
                          <input
                            type='radio'
                            id={`payment-${option.id}`}
                            name='payment-option'
                            checked={selectedPaymentOption?.id === option.id}
                            onChange={() => handlePaymentOptionChange(option)}
                          />
                          <label htmlFor={`payment-${option.id}`}>
                            {option.name}
                          </label>
                        </div>
                      ))}
                    </>
                  )}
                </div>
              </section>
            </section>
            <section className={styles.price_summary}>
              <button
                type='submit'
                disabled={createOrder.isPending}
                onClick={createOrderFn}
              >
                <ButtonState
                  isLoading={createOrder.isPending}
                  loadingText='Placing the Order...'
                  buttonText='Place Order'
                  spinnerSize={16}
                  spinnerColor='#fff'
                  spinnerType='clip'
                />
              </button>
            </section>
          </div>
        </div>
      )}
    </CheckoutPageWrapper>
  )
}

export default PaymentChoice
