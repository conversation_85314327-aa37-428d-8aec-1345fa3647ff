/* [project]/app/(shop)/(checkout-process)/components/layout/CartDisplaySection.module.scss.module.css [app-client] (css) */
.CartDisplaySection-module-scss-module__7Aiwqa__cart_display {
  flex-direction: column;
  gap: 1.5rem;
  display: flex;
}

@media (min-width: 768px) {
  .CartDisplaySection-module-scss-module__7Aiwqa__cart_display {
    flex-direction: row;
    gap: 2rem;
  }
}

.CartDisplaySection-module-scss-module__7Aiwqa__cart_display > :first-child {
  flex: 2;
}

.CartDisplaySection-module-scss-module__7Aiwqa__cart_display > :last-child {
  flex: 1;
  min-width: 300px;
}

/*# sourceMappingURL=e3841_components_layout_CartDisplaySection_module_scss_module_css_bad6b30c._.single.css.map*/