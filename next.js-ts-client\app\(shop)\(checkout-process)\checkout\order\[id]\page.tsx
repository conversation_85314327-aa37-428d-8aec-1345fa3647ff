'use client'

import payment_cards from '../../../../../../public/images/credit_cards.png'
import { loadStripe } from '@stripe/stripe-js'
import styles from './Order.module.scss'
import { Elements } from '@stripe/react-stripe-js'
import { AxiosError } from 'axios'
import { useOrder } from '@/src/hooks/order-hooks'
import { useClientSecret } from '@/src/hooks/checkout-hooks'
import EmptyCart from '@/src/components/utils/empty-cart/EmptyCart'
import Spinner from '@/src/components/utils/spinner/Spinner'
import { ErrorResponse } from '@/src/types/types'
import { getErrorMessage } from '@/src/components/utils/getErrorMessage'
import Link from 'next/link'
import Image from 'next/image'
import Alert from '@/src/components/utils/alert/Alert'
import Logo from '@/src/components/utils/logo/Logo'
import { use } from 'react'
import { DateTime } from 'luxon'
import { OrderedItemShape } from '@/src/types/order-types'
import LimitTitleLength from '@/src/components/utils/TextLimit'
import PayPalCheckout from '@/src/components/utils/paypal/PayPalCheckout'
import CheckoutForm from '../../../components/stripe-checkout/CheckoutForm'

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY!)

interface Props {
  params: Promise<{ id: number }>
}

const Order = ({ params }: Props) => {
  const { id } = use(params)

  // const orderId = Number(id)
  const { isPending, error, data: order } = useOrder(id)
  const { data: clientSecret } = useClientSecret(id)

  console.log('order id:', id)

  console.log('placed order:', order)

  console.log('clientSecret:', clientSecret)

  return (
    <div>
      {!id ? (
        <EmptyCart message='It seems that you have not placed any orders yet. Please add some products to the cart to checkout!' />
      ) : isPending ? (
        <Spinner color='#0091CF' size={20} loading={true} bgOpacity={0} />
      ) : error ? (
        <Alert
          variant='error'
          message={getErrorMessage(error as AxiosError<ErrorResponse>)}
        />
      ) : !order ? ( // If there  is no order with a given ID, Django with throw a 404 error
        <div className={styles.empty_cart}>
          <p>Your cart is empty. Add some products to the cart to checkout!</p>
          <Link href='/'>Go Shopping</Link>
        </div>
      ) : (
        <>
          <div className={`container`}>
            <h3 className={styles.order_summery_title}>Order Summary</h3>
            <div className={styles.checkout}>
              <section className={styles.order_summary}>
                <div className={styles.placed_at}>
                  <h3>Placed at: </h3>
                  <span>
                    {DateTime.fromISO(order.placed_at).toFormat(
                      'EEEE, MMMM d, yyyy, h:mm a'
                    )}
                  </span>
                </div>
                <div className={styles.contact_info}>
                  <div>
                    <h3>Contact Details: </h3>
                    <p>
                      Deliver to: {order.customer?.first_name}{' '}
                      {order.customer?.last_name}
                    </p>
                    <p>Phone: {order.customer?.phone_number}</p>
                    <p>Email to: {order.customer?.email}</p>
                  </div>
                  {/* <hr /> */}
                  <div className={styles.shipping_address}>
                    <h3>Shipping address: </h3>
                    <address>
                      {order.selected_address.full_name},<br />
                      {order.selected_address.street_name},<br />
                      {order.selected_address.postal_code},<br />
                      {order.selected_address.city_or_village}
                      <br />
                    </address>
                  </div>
                </div>
                <hr />
                <div className={styles.cart}>
                  <ul className={styles.cart__item_list}>
                    {order?.ordered_items?.map((item: OrderedItemShape) => (
                      <li key={item.id} className={styles.cart__cart_item}>
                        <div className={styles.cart_items_img}>
                          <Image
                            src={
                              item.product_variant?.product_image?.[0]?.image
                                ? `${process.env.NEXT_PUBLIC_CLOUDINARY_URL}/${item.product_variant.product_image[0].image}`
                                : '/images/no-image-placeholder.png'
                            }
                            alt={item.product.title || 'Product image'}
                            width={80}
                            height={80}
                            style={{ objectFit: 'cover' }}
                          />
                        </div>
                        <div className={styles.cart_items_info}>
                          <span>
                            <Link href={`/product/${item.product.slug}`}>
                              <LimitTitleLength
                                title={item.product.title}
                                maxLength={60}
                              />
                            </Link>
                          </span>
                          <span>
                            (${item.product_variant.price} x {item.quantity})
                          </span>
                          <span>
                            Variant:{' '}
                            {item.product_variant?.price_label?.attribute_value}
                          </span>
                          {Object.entries(item.extra_data).map(
                            ([key, value], index) => (
                              <div key={index}>
                                <p>{key} :</p>
                                <p>{value}</p>
                              </div>
                            )
                          )}
                        </div>
                        <div className={styles.cart_items_quantity}>
                          <p>Qty : {item.quantity}</p>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
                <hr />
                <div className={styles.payment_method}>
                  <h3>Payment Method:</h3>
                  <div>
                    {order?.payment_method.id == 2 && (
                      <Image
                        src={payment_cards}
                        alt='payment cards'
                        width={120}
                        height={30}
                      />
                    )}
                    <p>{order?.payment_method.name}</p>
                  </div>
                </div>
              </section>
              <section className={styles.pay_order}>
                <div>
                  <h3>Order Status:</h3>
                  <Alert
                    variant={
                      order?.order_status === 'Pending'
                        ? 'warning'
                        : order?.order_status === 'Processing'
                        ? 'info'
                        : order?.order_status === 'Dispatched'
                        ? 'info'
                        : 'success'
                    }
                    message={order?.order_status}
                  />
                </div>
                <div>
                  <h3>Payment Status:</h3>
                  <Alert
                    variant={
                      order?.payment_status === 'Pending'
                        ? 'warning'
                        : 'success'
                    }
                    message={order?.payment_status}
                  />
                </div>
                <div className={styles.payment_info}>
                  <div className={styles.total}>
                    <p>Total :</p>
                    <p>${order?.total}</p>
                  </div>
                </div>
                <div className={styles.checkout_card}>
                  {order?.payment_status === 'Pending' &&
                  order?.payment_method?.slug === 'paypal' ? (
                    <div className={styles.paypal_buttons}>
                      <p>Pay with PayPal:</p>
                      {/* <PaypalCheckout totalPrice={order?.total} orderId={order?.id} /> */}

                      {/* <BraintreeCheckout
                              amount={order?.total}
                              orderId={orderId}
                            /> */}

                      {/* <BraintreeWebCheckout
                              amount={order?.total}
                              orderId={orderId}
                            /> */}

                      <PayPalCheckout orderId={id} amount={order.total} />
                    </div>
                  ) : order?.payment_status === 'Pending' &&
                    order?.payment_method?.slug === 'stripe' ? (
                    <div className={styles.payment_options_buttons}>
                      {clientSecret && (
                        <Elements
                          // options={{
                          //   mode: 'payment',
                          //   amount: convertToCents(order?.total),
                          //   currency: 'usd',
                          //   // business: "PickyPC",
                          //   // appearance: {}, // Fully customizable with appearance API.
                          // }}

                          // options={{
                          //   clientSecret: order?.payment_intent_id, // Use backend-provided payment_intent_id
                          // }}

                          options={{
                            clientSecret: clientSecret.client_secret, // Pass the fetched clientSecret here
                          }}
                          stripe={stripePromise}
                        >
                          <CheckoutForm
                            // clientSecret={clientSecret}
                            amount={order?.total}
                            orderId={id}
                            // order={order}
                            // customer={{
                            //   email: order.customer.email,
                            //   name: `${order.customer.first_name} ${order.customer.last_name}`,
                            //   // address: {
                            //   //   line1: order.selected_address.address_line_1!,
                            //   //   city: order.selected_address.city_or_village,
                            //   //   postal_code: order.selected_address.postal_code,
                            //   //   country: 'USA'
                            //   // }
                            // }}
                          />
                        </Elements>
                      )}
                    </div>
                  ) : (
                    <div className={styles.purchase_done}>
                      {order?.payment_status === 'Paid' && (
                        <p>Thank you for your purchase.</p>
                      )}
                      <Link href='/account/orders'>My orders</Link>
                    </div>
                  )}
                </div>
              </section>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default Order
