'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import styles from './AddressStage.module.scss'
import authStore from '@/src/stores/auth-store'
import { useCustomerDetails } from '@/src/hooks/customer-hooks'
import cartStore from '@/src/stores/cart-store'
import { useCart, useDeleteCartItem } from '@/src/hooks/cart-hooks'
import { AddressFormInputs } from '@/src/components/account/addresses/ManageAddresses'
import Alert from '@/src/components/utils/alert/Alert'
import Logo from '@/src/components/utils/logo/Logo'
import CheckoutPageWrapper from '../../components/layout/CheckoutPageWrapper'
import ContactDetailsSection from '../../components/layout/ContactDetailsSection'
import CartDisplaySection from '../../components/layout/CartDisplaySection'

const AddressChoice = () => {
  const router = useRouter()

  const { isLoggedIn } = authStore()
  const { data: customer } = useCustomerDetails(isLoggedIn)
  const { setSelectedAddress, selectedAddress } = cartStore()
  const { data } = useCart()
  const { mutate: deleteCartItem } = useDeleteCartItem()

  const [addressesReady, setAddressesReady] = useState(false)

  useEffect(() => {
    if (customer?.address && customer.address.length > 0) {
      if (!selectedAddress || Object.keys(selectedAddress).length === 0) {
        setSelectedAddress(customer.address[0])
      }
      setAddressesReady(true)
    }
  }, [customer, selectedAddress, setSelectedAddress])

  // Handle out-of-stock items by removing them
  useEffect(() => {
    if (data && data?.cart_items?.length > 0) {
      const outOfStockItems = data.cart_items.filter(
        (item) => item.product_variant.stock_qty === 0
      )

      if (outOfStockItems.length > 0) {
        outOfStockItems.forEach((item) => {
          deleteCartItem(item.id) // Remove each out-of-stock item from the cart
        })
      }
    }
  }, [data, deleteCartItem])

  const handleAddressChange = (address: AddressFormInputs) => {
    setSelectedAddress(address)
  }

  return (
    <CheckoutPageWrapper>
      {customer?.address?.length === 0 || customer?.phone_number === '' ? (
        <>
          <div className='logo_header'>
            <Logo />
          </div>
          <div className={styles.missing_addresses}>
            <Alert
              variant='warning'
              textSize='20'
              message="
            You haven't added a shipping address yet.
            Please add one along with a phone number to continue with checkout. Thank you! 😊"
            />
            <section className='btn_container'>
              <button
                className='empty_btn'
                onClick={() => router.push('/account/profile')}
              >
                Update Profile
              </button>
            </section>
          </div>
        </>
      ) : (
        data && (
          <section>
            <section className={`container ${styles.address_stage}`}>
              <h3>Address Choices</h3>
              <ContactDetailsSection customer={customer} />
              <hr />
              <div className={styles.cart}>
                <CartDisplaySection
                  cartItems={data.cart_items}
                  totalPrice={data.total_price}
                  itemCount={data.item_count}
                  cartWeight={data.cart_weight}
                />
              </div>
              <hr />
              {/* Render the addresses only when addresses are ready */}
              {addressesReady && (
                <div className={styles.address_selection}>
                  <h3>Choose a shipping address: </h3>
                  {customer?.address?.map((address) => (
                    <address key={address.id}>
                      <input
                        type='radio'
                        id={`address-${address.id}`}
                        name='address'
                        value={address.id}
                        checked={selectedAddress?.id === address.id}
                        onChange={() => handleAddressChange(address)}
                      />
                      <label htmlFor={`address-${address.id}`}>
                        {address.full_name}, {address.street_name},{' '}
                        {address.city_or_village}
                      </label>
                    </address>
                  ))}
                  <button
                    onClick={() => router.push('/checkout/payment-choice')}
                  >
                    Use this address
                  </button>
                </div>
              )}
              <hr />
            </section>
          </section>
        )
      )}
    </CheckoutPageWrapper>
  )
}

export default AddressChoice
