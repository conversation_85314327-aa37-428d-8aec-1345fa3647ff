/* [project]/app/(shop)/(checkout-process)/checkout/address-choice/AddressStage.module.scss.module.css [app-client] (css) */
.AddressStage-module-scss-module__cybDRG__missing_addresses {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.AddressStage-module-scss-module__cybDRG__address_stage {
  padding: 0 .5rem;
}

.AddressStage-module-scss-module__cybDRG__address_stage > h3:first-child {
  color: #333;
  text-align: center;
  background-color: #d4f4ff;
  padding: .5rem 0;
  font-size: 25px;
  font-weight: bold;
}

.AddressStage-module-scss-module__cybDRG__contact_details {
  margin: 10px 0;
}

.AddressStage-module-scss-module__cybDRG__contact_details h3 {
  color: #0091cf;
  font-size: 18px;
  font-weight: bold;
}

.AddressStage-module-scss-module__cybDRG__cart {
  grid-template-columns: 1fr;
  gap: 1rem;
  margin: 1rem 0;
  display: grid;
}

.AddressStage-module-scss-module__cybDRG__address_selection {
  margin: 1rem 0;
}

.AddressStage-module-scss-module__cybDRG__address_selection address {
  flex-flow: row;
  justify-content: flex-start;
  align-items: center;
  column-gap: .4rem;
  margin: 0 0 0 2rem;
  font-style: normal;
  display: flex;
}

.AddressStage-module-scss-module__cybDRG__address_selection address input {
  margin: .5rem 0;
}

.AddressStage-module-scss-module__cybDRG__address_selection h3 {
  color: #0091cf;
  margin: 10px 0;
  font-size: 18px;
  font-weight: bold;
}

.AddressStage-module-scss-module__cybDRG__address_selection button {
  color: #fff;
  text-transform: uppercase;
  letter-spacing: .7px;
  background-color: #00b3ff;
  border: 2px solid #00b3ff;
  border-radius: 3px;
  flex-flow: row;
  justify-content: center;
  align-items: center;
  margin: 1rem auto;
  padding: .5rem 1.5rem;
  font-weight: bold;
  transition: all .3s;
  display: flex;
}

.AddressStage-module-scss-module__cybDRG__address_selection button:hover {
  color: #d9d9d9;
  background-color: #008fcc;
  border: 2px solid #008fcc;
}

@media not (max-width: 768px) {
  .AddressStage-module-scss-module__cybDRG__cart {
    grid-template-columns: 2fr 1fr;
  }
}

/* [project]/src/components/utils/empty-cart/EmptyCart.module.scss.module.css [app-client] (css) */
.EmptyCart-module-scss-module__p5kxma__empty_cart {
  text-align: center;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  max-width: 500px;
  margin: 3rem auto 0;
  padding: 20px;
  display: flex;
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__icon_section {
  margin-bottom: 15px;
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__icon_section svg {
  color: #666;
  font-size: 64px;
}

@media (max-width: 576px) {
  .EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__icon_section svg {
    font-size: 48px;
  }
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__content_section {
  margin-bottom: 20px;
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__content_section .EmptyCart-module-scss-module__p5kxma__heading {
  color: #333;
  margin-bottom: 12px;
  font-size: 20px;
  font-weight: 700;
  line-height: 1.3;
}

@media (max-width: 576px) {
  .EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__content_section .EmptyCart-module-scss-module__p5kxma__heading {
    font-size: 18px;
  }
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__content_section .EmptyCart-module-scss-module__p5kxma__description {
  color: #666;
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

@media (max-width: 576px) {
  .EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__content_section .EmptyCart-module-scss-module__p5kxma__description {
    font-size: 16px;
  }
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__action_section .EmptyCart-module-scss-module__p5kxma__action_button {
  color: #fff;
  background-color: #0091cf;
  border-radius: 3px;
  flex-flow: row;
  justify-content: center;
  align-items: center;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  transition: background-color .3s, transform .2s;
  display: inline-block;
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__action_section .EmptyCart-module-scss-module__p5kxma__action_button:hover {
  background-color: #00b3ff;
  text-decoration: none;
  transform: translateY(-1px);
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__action_section .EmptyCart-module-scss-module__p5kxma__action_button:focus {
  outline-offset: 2px;
  outline: 2px solid #0091cf;
}

.EmptyCart-module-scss-module__p5kxma__empty_cart .EmptyCart-module-scss-module__p5kxma__action_section .EmptyCart-module-scss-module__p5kxma__action_button:active {
  transform: translateY(0);
}

/* [project]/src/components/utils/underlay/Underlay.module.scss.module.css [app-client] (css) */
.Underlay-module-scss-module__PkWa8a__underlay {
  z-index: 20;
  flex-flow: row;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  display: flex;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

/* [project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.module.scss.module.css [app-client] (css) */
.CartItemsList-module-scss-module__kultEW__cart_item {
  grid-template-columns: 120px 1fr;
  gap: 1rem;
  width: 100%;
  height: -moz-fit-content;
  height: fit-content;
  margin: 0;
  padding: 1rem;
  display: grid;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, .05), inset 0 0 0 1px #d1d5db;
}

.CartItemsList-module-scss-module__kultEW__cart_item__img img {
  width: 100%;
}

.CartItemsList-module-scss-module__kultEW__cart_item__info {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: .5rem;
  display: flex;
}

.CartItemsList-module-scss-module__kultEW__cart_item__info span:first-child a {
  color: #131921;
}

.CartItemsList-module-scss-module__kultEW__cart_item__info span:first-child a:hover {
  text-decoration: underline;
}

.CartItemsList-module-scss-module__kultEW__cart_item__info .CartItemsList-module-scss-module__kultEW__cart_item__title {
  font-size: 17.5px;
  font-weight: bold;
}

.CartItemsList-module-scss-module__kultEW__cart_item__info .CartItemsList-module-scss-module__kultEW__cart_item__title:hover {
  color: #0091cf;
}

.CartItemsList-module-scss-module__kultEW__cart_item__info .CartItemsList-module-scss-module__kultEW__cart_item__extra_data {
  color: #666;
  flex-flow: row;
  justify-content: flex-start;
  align-items: center;
  gap: .5rem;
  font-size: 14.5px;
  display: flex;
}

.CartItemsList-module-scss-module__kultEW__cart_item__info .CartItemsList-module-scss-module__kultEW__cart_item__extra_data p:first-child {
  font-weight: bold;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity {
  flex-flow: column;
  grid-column: 1 / 3;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child {
  flex-flow: row;
  justify-content: flex-start;
  align-items: center;
  column-gap: .8rem;
  display: flex;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child p {
  color: #333;
  font-weight: bold;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button {
  border: 1.6px solid #fff;
  border-radius: 2px;
  padding: 6px;
  transition: all .2s ease-out;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button i {
  flex-flow: row;
  justify-content: center;
  align-items: center;
  display: flex;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button:hover {
  color: #0091cf;
  border: 1.6px solid #0091cf;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button:disabled:hover {
  color: inherit;
  cursor: not-allowed;
  border: 1.6px solid #fff;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button:nth-child(5) {
  background-color: #f00000;
  border: 1.6px solid #f00000;
  transition: all .3s;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button:nth-child(5) i {
  color: #fff;
  font-weight: bold;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button:nth-child(5):hover {
  background-color: #bd0000;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity div:first-child button:nth-child(5):hover i {
  color: #fff;
}

.CartItemsList-module-scss-module__kultEW__cart_item__quantity p:last-child {
  color: #cf0707;
  text-transform: none;
  margin: 10px 0;
  font-weight: bold;
}

@media not (max-width: 768px) {
  .CartItemsList-module-scss-module__kultEW__cart_item {
    grid-template-columns: 100px 2fr 1fr;
  }

  .CartItemsList-module-scss-module__kultEW__cart_item__quantity {
    grid-column: 3 / 4;
    justify-content: center;
    width: max-content;
  }
}

/* [project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.module.scss.module.css [app-client] (css) */
.PriceSummary-module-scss-module__7p8iVa__cart__checkout {
  background-color: #d4f4ff;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 1.5rem;
  display: flex;
}

.PriceSummary-module-scss-module__7p8iVa__cart__checkout div {
  flex-flow: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  font-size: 1.2rem;
  display: flex;
}

.PriceSummary-module-scss-module__7p8iVa__cart__checkout div p:first-child {
  color: #333;
  font-weight: bold;
}

.PriceSummary-module-scss-module__7p8iVa__cart__checkout div p:last-child {
  color: #0091cf;
  font-weight: bold;
}

.PriceSummary-module-scss-module__7p8iVa__cart__checkout div:nth-child(2) p:first-child {
  flex-flow: row;
  justify-content: flex-start;
  align-items: center;
  column-gap: 2px;
  display: flex;
}

.PriceSummary-module-scss-module__7p8iVa__cart__checkout button {
  color: #fff;
  text-transform: uppercase;
  letter-spacing: .7px;
  background-color: #00b3ff;
  border-radius: 3px;
  flex-flow: row;
  justify-content: center;
  align-self: center;
  align-items: center;
  width: 100%;
  padding: 1rem 0;
  font-weight: bold;
  transition: all .3s;
  display: flex;
}

.PriceSummary-module-scss-module__7p8iVa__cart__checkout button:hover {
  color: #d9d9d9;
  background-color: #008fcc;
}

/* [project]/src/components/utils/tooltip/Tooltip.module.scss.module.css [app-client] (css) */
.Tooltip-module-scss-module__vdbe-W__tooltip {
  cursor: pointer;
  position: relative;
}

.Tooltip-module-scss-module__vdbe-W__tooltip:after {
  content: attr(data-tooltip);
  color: #fff;
  opacity: 0;
  visibility: hidden;
  white-space: nowrap;
  pointer-events: none;
  z-index: 1000;
  background-color: #333;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  transition: opacity .2s, visibility .3s;
  position: absolute;
}

.Tooltip-module-scss-module__vdbe-W__tooltip--top:after {
  bottom: calc(100% + 6px);
  left: 50%;
  transform: translateX(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--bottom:after {
  top: calc(100% + 6px);
  left: 50%;
  transform: translateX(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--left:after {
  top: 50%;
  right: calc(100% + 6px);
  transform: translateY(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--right:after {
  top: 50%;
  left: calc(100% + 6px);
  transform: translateY(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--hover:hover:after, .Tooltip-module-scss-module__vdbe-W__tooltip--condition:after {
  opacity: 1;
  visibility: visible;
}

.Tooltip-module-scss-module__vdbe-W__tooltip:before {
  content: "";
  opacity: 0;
  visibility: hidden;
  z-index: 1001;
  border: 4px solid rgba(0, 0, 0, 0);
  transition: opacity .2s, visibility .3s;
  position: absolute;
}

.Tooltip-module-scss-module__vdbe-W__tooltip--top:before {
  border-top-color: #333;
  bottom: calc(100% + 2px);
  left: 50%;
  transform: translateX(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--bottom:before {
  border-bottom-color: #333;
  top: calc(100% + 2px);
  left: 50%;
  transform: translateX(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--left:before {
  border-left-color: #333;
  top: 50%;
  right: calc(100% + 2px);
  transform: translateY(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--right:before {
  border-right-color: #333;
  top: 50%;
  left: calc(100% + 2px);
  transform: translateY(-50%);
}

.Tooltip-module-scss-module__vdbe-W__tooltip--hover:hover:before, .Tooltip-module-scss-module__vdbe-W__tooltip--condition:before {
  opacity: 1;
  visibility: visible;
}

@media (max-width: 768px) {
  .Tooltip-module-scss-module__vdbe-W__tooltip:after {
    white-space: normal;
    word-wrap: break-word;
    max-width: 200px;
    padding: 3px 6px;
    font-size: 11px;
  }
}

/*# sourceMappingURL=_47d13dd6._.css.map*/