{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/app/(shop)/(checkout-process)/components/layout/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/(shop)/(checkout-process)/components/layout/ContactDetailsSection.module.scss"], "sourcesContent": [".contact_info {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n\n  @media (min-width: 768px) {\n    flex-direction: row;\n    justify-content: space-between;\n  }\n}\n\n.contact_details {\n  h3 {\n    margin-bottom: 0.5rem;\n    color: #333;\n  }\n\n  p {\n    margin: 0.25rem 0;\n    color: #666;\n  }\n}\n\n.shipping_address {\n  h3 {\n    margin-bottom: 0.5rem;\n    color: #333;\n  }\n\n  address {\n    font-style: normal;\n    color: #666;\n    line-height: 1.4;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;AAKE;EALF;;;;;;AAYE;;;;;AAKA;;;;;AAOA;;;;;AAKA"}}]}