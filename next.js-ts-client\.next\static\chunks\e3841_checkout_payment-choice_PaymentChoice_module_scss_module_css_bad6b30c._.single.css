/* [project]/app/(shop)/(checkout-process)/checkout/payment-choice/PaymentChoice.module.scss.module.css [app-client] (css) */
.PaymentChoice-module-scss-module__SCceZW__place_order {
  color: #333;
  text-align: center;
  background-color: #d4f4ff;
  padding: .5rem 0;
  font-size: 25px;
  font-weight: bold;
}

.PaymentChoice-module-scss-module__SCceZW__payment_options_stage {
  grid-template-columns: 1fr;
  display: grid;
}

.PaymentChoice-module-scss-module__SCceZW__logo {
  background-color: #131921;
  flex-flow: row;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  display: flex;
}

.PaymentChoice-module-scss-module__SCceZW__contact_info {
  flex-flow: row;
  justify-content: space-around;
  align-items: center;
  padding: 1rem 0;
  display: flex;
}

.PaymentChoice-module-scss-module__SCceZW__contact_info div h3 {
  color: #0091cf;
  font-size: 18px;
  font-weight: bold;
}

.PaymentChoice-module-scss-module__SCceZW__contact_details h3, .PaymentChoice-module-scss-module__SCceZW__shipping_address h3 {
  font-weight: bold;
}

.PaymentChoice-module-scss-module__SCceZW__contact_details address, .PaymentChoice-module-scss-module__SCceZW__shipping_address address {
  font-style: normal;
}

.PaymentChoice-module-scss-module__SCceZW__cart {
  grid-template-columns: 1fr;
  gap: 1rem;
  margin: 1rem 0;
  display: grid;
}

.PaymentChoice-module-scss-module__SCceZW__cart_items_quantity {
  flex-flow: row;
  justify-content: center;
  align-items: center;
  column-gap: .5rem;
  font-weight: bold;
  display: flex;
}

.PaymentChoice-module-scss-module__SCceZW__payment_options {
  margin: 1rem auto;
  padding: 0 0 0 1rem;
}

.PaymentChoice-module-scss-module__SCceZW__payment_options div {
  flex-flow: row;
  justify-content: flex-start;
  align-items: center;
  column-gap: .4rem;
  margin: 0 0 0 2rem;
  display: flex;
}

.PaymentChoice-module-scss-module__SCceZW__payment_options div input {
  margin: .5rem 0;
}

.PaymentChoice-module-scss-module__SCceZW__payment_options h3 {
  color: #0091cf;
  margin: 10px 0;
  font-size: 18px;
  font-weight: bold;
}

.PaymentChoice-module-scss-module__SCceZW__price_summary {
  background-color: #d4f4ff;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 1.5rem;
  display: flex;
}

.PaymentChoice-module-scss-module__SCceZW__price_summary .PaymentChoice-module-scss-module__SCceZW__prices {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  row-gap: 1rem;
  font-size: 16px;
  display: flex;
}

.PaymentChoice-module-scss-module__SCceZW__price_summary .PaymentChoice-module-scss-module__SCceZW__prices div {
  flex-flow: row;
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.PaymentChoice-module-scss-module__SCceZW__price_summary .PaymentChoice-module-scss-module__SCceZW__prices div:last-child p:last-child {
  font-size: 18px;
}

.PaymentChoice-module-scss-module__SCceZW__price_summary .PaymentChoice-module-scss-module__SCceZW__prices p:first-child {
  color: #333;
  font-weight: bold;
}

.PaymentChoice-module-scss-module__SCceZW__price_summary .PaymentChoice-module-scss-module__SCceZW__prices p:last-child {
  color: #0091cf;
  background-color: #e0adad;
  font-weight: bold;
}

.PaymentChoice-module-scss-module__SCceZW__price_summary button {
  color: #fff;
  text-transform: uppercase;
  letter-spacing: .7px;
  background-color: #00b3ff;
  border-radius: 3px;
  flex-flow: row;
  justify-content: center;
  align-items: center;
  width: calc(100% - 3rem);
  margin: 0 1.5rem;
  padding: 1rem 0;
  font-weight: bold;
  transition: all .3s;
  display: flex;
}

.PaymentChoice-module-scss-module__SCceZW__price_summary button:disabled {
  color: #666;
  cursor: not-allowed;
  background-color: #ccc;
}

.PaymentChoice-module-scss-module__SCceZW__price_summary button:hover {
  color: #d9d9d9;
  background-color: #008fcc;
}

@media not (max-width: 768px) {
  .PaymentChoice-module-scss-module__SCceZW__payment_options_stage {
    grid-template-columns: 2fr 1fr;
    gap: 1rem;
    margin: 1rem 0 0;
  }

  .PaymentChoice-module-scss-module__SCceZW__contact_info {
    flex-flow: row;
    justify-content: flex-start;
    align-items: flex-start;
    column-gap: 4rem;
    padding: 0 0 1rem;
    display: flex;
  }
}

/*# sourceMappingURL=e3841_checkout_payment-choice_PaymentChoice_module_scss_module_css_bad6b30c._.single.css.map*/