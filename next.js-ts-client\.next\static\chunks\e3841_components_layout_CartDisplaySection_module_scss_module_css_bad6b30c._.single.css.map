{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/app/(shop)/(checkout-process)/components/layout/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/(shop)/(checkout-process)/components/layout/CartDisplaySection.module.scss"], "sourcesContent": [".cart_display {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n\n  @media (min-width: 768px) {\n    flex-direction: row;\n    gap: 2rem;\n  }\n\n  // Ensure cart items list takes up more space\n  > :first-child {\n    flex: 2;\n  }\n\n  // Price summary takes less space\n  > :last-child {\n    flex: 1;\n    min-width: 300px;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;AAKE;EALF;;;;;;AAWE;;;;AAKA"}}]}