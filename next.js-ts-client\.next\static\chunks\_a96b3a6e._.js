(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/app/(shop)/(checkout-process)/checkout/address-choice/AddressStage.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "address_selection": "AddressStage-module-scss-module__cybDRG__address_selection",
  "address_stage": "AddressStage-module-scss-module__cybDRG__address_stage",
  "cart": "AddressStage-module-scss-module__cybDRG__cart",
  "contact_details": "AddressStage-module-scss-module__cybDRG__contact_details",
  "missing_addresses": "AddressStage-module-scss-module__cybDRG__missing_addresses",
});
}),
"[project]/src/components/utils/empty-cart/EmptyCart.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "action_button": "EmptyCart-module-scss-module__p5kxma__action_button",
  "action_section": "EmptyCart-module-scss-module__p5kxma__action_section",
  "content_section": "EmptyCart-module-scss-module__p5kxma__content_section",
  "description": "EmptyCart-module-scss-module__p5kxma__description",
  "empty_cart": "EmptyCart-module-scss-module__p5kxma__empty_cart",
  "heading": "EmptyCart-module-scss-module__p5kxma__heading",
  "icon_section": "EmptyCart-module-scss-module__p5kxma__icon_section",
});
}),
"[project]/src/components/utils/empty-cart/EmptyCart.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/utils/empty-cart/EmptyCart.module.scss [app-client] (css module)");
;
;
;
;
const EmptyCart = (param)=>{
    let { message, showIcon = true, actionText = "Go Shopping", actionHref = "/" } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].empty_cart,
        role: "region",
        "aria-label": "Empty cart",
        children: [
            showIcon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].icon_section,
                "aria-hidden": "true",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaShoppingCart"], {}, void 0, false, {
                    fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
                    lineNumber: 22,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
                lineNumber: 21,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].content_section,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].heading,
                        children: message ? message : "Your cart is empty"
                    }, void 0, false, {
                        fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
                        lineNumber: 27,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].description,
                        children: "Add some products to your cart to get started with your shopping journey."
                    }, void 0, false, {
                        fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
                        lineNumber: 30,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].action_section,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    href: actionHref,
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].action_button,
                    "aria-label": "".concat(actionText, " - Browse products to add to your cart"),
                    children: actionText
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
                    lineNumber: 36,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
                lineNumber: 35,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/utils/empty-cart/EmptyCart.tsx",
        lineNumber: 19,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = EmptyCart;
const __TURBOPACK__default__export__ = EmptyCart;
var _c;
__turbopack_context__.k.register(_c, "EmptyCart");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/utils/underlay/Underlay.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "underlay": "Underlay-module-scss-module__PkWa8a__underlay",
});
}),
"[project]/src/components/utils/underlay/Underlay.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$underlay$2f$Underlay$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/utils/underlay/Underlay.module.scss [app-client] (css module)");
;
;
const Underlay = (param)=>{
    let { children, isOpen, onClose, bgOpacity = 0.3 } = param;
    const handleOverlayClick = (e)=>{
        if (e.target === e.currentTarget && onClose) {
            onClose();
        }
    };
    return isOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$underlay$2f$Underlay$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].underlay,
        style: {
            backgroundColor: "rgba(0, 0, 0, ".concat(bgOpacity, ")")
        },
        onClick: handleOverlayClick,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/utils/underlay/Underlay.tsx",
        lineNumber: 21,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0)) : null;
};
_c = Underlay;
const __TURBOPACK__default__export__ = Underlay;
var _c;
__turbopack_context__.k.register(_c, "Underlay");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/utils/spinner/Spinner.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$SyncLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-spinners/SyncLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$ClipLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-spinners/ClipLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$PulseLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-spinners/PulseLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$RiseLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-spinners/RiseLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$RotateLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-spinners/RotateLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$ScaleLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-spinners/ScaleLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$CircleLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-spinners/CircleLoader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$underlay$2f$Underlay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/underlay/Underlay.tsx [app-client] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
const Spinner = (param)=>{
    let { loading, color, size = 150, thickness = 5, bgOpacity, useUnderlay = false, spinnerType = 'sync' } = param;
    const renderSpinner = ()=>{
        const commonProps = {
            color,
            loading,
            size,
            thickness,
            'aria-label': 'Loading Spinner'
        };
        switch(spinnerType){
            case 'clip':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$ClipLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    ...commonProps
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
                    lineNumber: 42,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'pulse':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$PulseLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    ...commonProps
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
                    lineNumber: 44,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'rise':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$RiseLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    ...commonProps
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
                    lineNumber: 46,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'rotate':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$RotateLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    ...commonProps
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
                    lineNumber: 48,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'scale':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$ScaleLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    ...commonProps
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
                    lineNumber: 50,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'circle':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$CircleLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    ...commonProps
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
                    lineNumber: 52,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
            case 'sync':
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$spinners$2f$SyncLoader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    ...commonProps
                }, void 0, false, {
                    fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
                    lineNumber: 55,
                    columnNumber: 16
                }, ("TURBOPACK compile-time value", void 0));
        }
    };
    if (!useUnderlay) {
        return loading ? renderSpinner() : null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$underlay$2f$Underlay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        isOpen: loading,
        bgOpacity: bgOpacity,
        children: renderSpinner()
    }, void 0, false, {
        fileName: "[project]/src/components/utils/spinner/Spinner.tsx",
        lineNumber: 64,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = Spinner;
const __TURBOPACK__default__export__ = Spinner;
var _c;
__turbopack_context__.k.register(_c, "Spinner");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/utils/TextLimit.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
const LimitTitleLength = (param)=>{
    let { title, maxLength } = param;
    function limitTitleLength(title, maxLength) {
        if (title.length > maxLength) {
            return title.substring(0, maxLength) + '...';
        }
        return title;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: limitTitleLength(title, maxLength)
    }, void 0, false);
};
_c = LimitTitleLength;
const __TURBOPACK__default__export__ = LimitTitleLength;
var _c;
__turbopack_context__.k.register(_c, "LimitTitleLength");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "cart_item": "CartItemsList-module-scss-module__kultEW__cart_item",
  "cart_item__extra_data": "CartItemsList-module-scss-module__kultEW__cart_item__extra_data",
  "cart_item__img": "CartItemsList-module-scss-module__kultEW__cart_item__img",
  "cart_item__info": "CartItemsList-module-scss-module__kultEW__cart_item__info",
  "cart_item__quantity": "CartItemsList-module-scss-module__kultEW__cart_item__quantity",
  "cart_item__title": "CartItemsList-module-scss-module__kultEW__cart_item__title",
});
}),
"[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fi/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$TextLimit$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/TextLimit.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.module.scss [app-client] (css module)");
'use client';
;
;
;
;
;
const CartItemsList = (param)=>{
    let { cartItems, handleIncrement, handleDecrement, deleteCartItem } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
        children: cartItems === null || cartItems === void 0 ? void 0 : cartItems.map((item)=>{
            var _item_product_variant_product_image_, _item_product_variant_product_image, _item_product_variant, _item_product_variant_product_image_1, _item_product_variant_product_image1, _item_product_variant1, _item_product_variant_price_label, _item_product_variant2;
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart_item,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart_item__img,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                            src: ((_item_product_variant = item.product_variant) === null || _item_product_variant === void 0 ? void 0 : (_item_product_variant_product_image = _item_product_variant.product_image) === null || _item_product_variant_product_image === void 0 ? void 0 : (_item_product_variant_product_image_ = _item_product_variant_product_image[0]) === null || _item_product_variant_product_image_ === void 0 ? void 0 : _item_product_variant_product_image_.image) ? "".concat(("TURBOPACK compile-time value", "https://res.cloudinary.com/dev-kani"), "/").concat(item.product_variant.product_image[0].image) : noImagePlaceholder,
                            alt: ((_item_product_variant1 = item.product_variant) === null || _item_product_variant1 === void 0 ? void 0 : (_item_product_variant_product_image1 = _item_product_variant1.product_image) === null || _item_product_variant_product_image1 === void 0 ? void 0 : (_item_product_variant_product_image_1 = _item_product_variant_product_image1[0]) === null || _item_product_variant_product_image_1 === void 0 ? void 0 : _item_product_variant_product_image_1.alternative_text) || item.product.title
                        }, void 0, false, {
                            fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                            lineNumber: 23,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                        lineNumber: 22,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart_item__info,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart_item__title,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/product/".concat(item.product.slug, "/"),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$TextLimit$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        title: item.product.title,
                                        maxLength: 60
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                        lineNumber: 33,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                    lineNumber: 32,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                lineNumber: 31,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            " ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    "($",
                                    item.product_variant.price,
                                    " x ",
                                    item.quantity,
                                    ")"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                lineNumber: 36,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    "variant: ",
                                    (_item_product_variant2 = item.product_variant) === null || _item_product_variant2 === void 0 ? void 0 : (_item_product_variant_price_label = _item_product_variant2.price_label) === null || _item_product_variant_price_label === void 0 ? void 0 : _item_product_variant_price_label.attribute_value
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                lineNumber: 37,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            Object.entries(item.extra_data).map((param, index)=>{
                                let [key, value] = param;
                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart_item__extra_data,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: [
                                                key,
                                                " :"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                            lineNumber: 40,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: value
                                        }, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                            lineNumber: 40,
                                            columnNumber: 31
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, index, true, {
                                    fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                    lineNumber: 39,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0));
                            })
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                        lineNumber: 30,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart_item__quantity,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: "Qty:"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                        lineNumber: 46,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    handleDecrement && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>handleDecrement(item),
                                        disabled: item.product_variant.stock_qty === 0,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiMinus"], {}, void 0, false, {
                                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                lineNumber: 49,
                                                columnNumber: 68
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                            lineNumber: 49,
                                            columnNumber: 65
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                        lineNumber: 47,
                                        columnNumber: 35
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        children: item.quantity
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                        lineNumber: 50,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    handleIncrement && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>handleIncrement(item),
                                        disabled: item.product_variant.stock_qty === 0,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiPlus"], {}, void 0, false, {
                                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                lineNumber: 53,
                                                columnNumber: 68
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                            lineNumber: 53,
                                            columnNumber: 65
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                        lineNumber: 51,
                                        columnNumber: 35
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    deleteCartItem && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>deleteCartItem(item.id),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fi$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FiTrash2"], {}, void 0, false, {
                                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                                lineNumber: 54,
                                                columnNumber: 85
                                            }, ("TURBOPACK compile-time value", void 0))
                                        }, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                            lineNumber: 54,
                                            columnNumber: 82
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                        lineNumber: 54,
                                        columnNumber: 34
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                lineNumber: 45,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            item.product_variant.stock_qty === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: "Out of Stock"
                            }, void 0, false, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                                lineNumber: 56,
                                columnNumber: 54
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                        lineNumber: 44,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, item.id, true, {
                fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
                lineNumber: 21,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0));
        })
    }, void 0, false, {
        fileName: "[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx",
        lineNumber: 19,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = CartItemsList;
const __TURBOPACK__default__export__ = CartItemsList;
var _c;
__turbopack_context__.k.register(_c, "CartItemsList");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "cart__checkout": "PriceSummary-module-scss-module__7p8iVa__cart__checkout",
});
}),
"[project]/src/components/utils/tooltip/Tooltip.module.scss [app-client] (css module)", ((__turbopack_context__) => {

__turbopack_context__.v({
  "tooltip": "Tooltip-module-scss-module__vdbe-W__tooltip",
  "tooltip--bottom": "Tooltip-module-scss-module__vdbe-W__tooltip--bottom",
  "tooltip--condition": "Tooltip-module-scss-module__vdbe-W__tooltip--condition",
  "tooltip--hover": "Tooltip-module-scss-module__vdbe-W__tooltip--hover",
  "tooltip--left": "Tooltip-module-scss-module__vdbe-W__tooltip--left",
  "tooltip--right": "Tooltip-module-scss-module__vdbe-W__tooltip--right",
  "tooltip--top": "Tooltip-module-scss-module__vdbe-W__tooltip--top",
});
}),
"[project]/src/components/utils/tooltip/Tooltip.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$tooltip$2f$Tooltip$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/src/components/utils/tooltip/Tooltip.module.scss [app-client] (css module)");
;
;
const Tooltip = (param)=>{
    let { children, content, position = 'top', disabled = false, className = '', showOnHover = true, showOnCondition = false } = param;
    // Don't show tooltip if disabled or no content
    if (disabled || !content) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: children
        }, void 0, false);
    }
    const tooltipClasses = [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$tooltip$2f$Tooltip$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].tooltip,
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$tooltip$2f$Tooltip$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"]["tooltip--".concat(position)],
        showOnHover ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$tooltip$2f$Tooltip$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"]['tooltip--hover'] : '',
        showOnCondition ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$tooltip$2f$Tooltip$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"]['tooltip--condition'] : '',
        className
    ].filter(Boolean).join(' ');
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: tooltipClasses,
        "data-tooltip": content,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/utils/tooltip/Tooltip.tsx",
        lineNumber: 37,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = Tooltip;
const __TURBOPACK__default__export__ = Tooltip;
var _c;
__turbopack_context__.k.register(_c, "Tooltip");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ri$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/ri/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$price$2d$summary$2f$PriceSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.module.scss [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/tooltip/Tooltip.tsx [app-client] (ecmascript)");
;
;
;
;
const PriceSummary = (param)=>{
    let { totalPrice, item_count, cart_weight, onCheckout } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$price$2d$summary$2f$PriceSummary$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart__checkout,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "Item count: "
                    }, void 0, false, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx",
                        lineNumber: 25,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    " ",
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: item_count
                    }, void 0, false, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx",
                        lineNumber: 25,
                        columnNumber: 29
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx",
                lineNumber: 23,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "Cart weight:",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$tooltip$2f$Tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                content: "Shipping cost will be calculated after finalizing adding items to the cart.",
                                position: "top",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$ri$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RiQuestionFill"], {}, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx",
                                        lineNumber: 45,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx",
                                    lineNumber: 44,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx",
                                lineNumber: 40,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx",
                        lineNumber: 38,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            cart_weight,
                            "g"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx",
                        lineNumber: 49,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx",
                lineNumber: 27,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "Item's cost: "
                    }, void 0, false, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx",
                        lineNumber: 55,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    " ",
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "$",
                            totalPrice.toFixed(2)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx",
                        lineNumber: 55,
                        columnNumber: 35
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx",
                lineNumber: 54,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            onCheckout && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: onCheckout,
                children: "Proceed to checkout"
            }, void 0, false, {
                fileName: "[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx",
                lineNumber: 57,
                columnNumber: 22
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx",
        lineNumber: 22,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = PriceSummary;
const __TURBOPACK__default__export__ = PriceSummary;
var _c;
__turbopack_context__.k.register(_c, "PriceSummary");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$checkout$2f$address$2d$choice$2f$AddressStage$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/app/(shop)/(checkout-process)/checkout/address-choice/AddressStage.module.scss [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/auth-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$customer$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/customer-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/cart-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/cart-hooks.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/empty-cart/EmptyCart.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/alert/Alert.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$logo$2f$Logo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/logo/Logo.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$spinner$2f$Spinner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/utils/spinner/Spinner.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(shop)/(checkout-process)/components/cart/CartItemsList.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$price$2d$summary$2f$PriceSummary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(shop)/(checkout-process)/components/price-summary/PriceSummary.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const AddressChoice = ()=>{
    var _customer_address, _customer_address1;
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { isLoggedIn } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$auth$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const { data: customer } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$customer$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCustomerDetails"])(isLoggedIn);
    const { cartId, setSelectedAddress, selectedAddress } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$cart$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const { isPending, error, data } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCart"])();
    const { mutate: deleteCartItem } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDeleteCartItem"])();
    const [addressesReady, setAddressesReady] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AddressChoice.useEffect": ()=>{
            if (!isLoggedIn) {
                router.push('/login');
            }
        }
    }["AddressChoice.useEffect"], [
        isLoggedIn,
        router
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AddressChoice.useEffect": ()=>{
            if ((customer === null || customer === void 0 ? void 0 : customer.address) && customer.address.length > 0) {
                if (!selectedAddress || Object.keys(selectedAddress).length === 0) {
                    setSelectedAddress(customer.address[0]);
                }
                setAddressesReady(true);
            }
        }
    }["AddressChoice.useEffect"], [
        customer,
        selectedAddress,
        setSelectedAddress
    ]);
    // Handle out-of-stock items by removing them
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AddressChoice.useEffect": ()=>{
            var _data_cart_items;
            if (data && (data === null || data === void 0 ? void 0 : (_data_cart_items = data.cart_items) === null || _data_cart_items === void 0 ? void 0 : _data_cart_items.length) > 0) {
                const outOfStockItems = data.cart_items.filter({
                    "AddressChoice.useEffect.outOfStockItems": (item)=>item.product_variant.stock_qty === 0
                }["AddressChoice.useEffect.outOfStockItems"]);
                if (outOfStockItems.length > 0) {
                    outOfStockItems.forEach({
                        "AddressChoice.useEffect": (item)=>{
                            deleteCartItem(item.id); // Remove each out-of-stock item from the cart
                        }
                    }["AddressChoice.useEffect"]);
                }
            }
        }
    }["AddressChoice.useEffect"], [
        data,
        deleteCartItem
    ]);
    const handleAddressChange = (address)=>{
        setSelectedAddress(address);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: !cartId ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$empty$2d$cart$2f$EmptyCart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            message: "Your cart is empty. Add some products to the cart to checkout!"
        }, void 0, false, {
            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
            lineNumber: 67,
            columnNumber: 9
        }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: isPending ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$spinner$2f$Spinner$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                color: "#0091CF",
                size: 20,
                loading: true
            }, void 0, false, {
                fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                lineNumber: 71,
                columnNumber: 13
            }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: error ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    variant: "error",
                    message: error.message
                }, void 0, false, {
                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                    lineNumber: 75,
                    columnNumber: 17
                }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                    children: (customer === null || customer === void 0 ? void 0 : (_customer_address = customer.address) === null || _customer_address === void 0 ? void 0 : _customer_address.length) === 0 || (customer === null || customer === void 0 ? void 0 : customer.phone_number) === '' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "logo_header",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$logo$2f$Logo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                    lineNumber: 82,
                                    columnNumber: 25
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                lineNumber: 81,
                                columnNumber: 23
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$checkout$2f$address$2d$choice$2f$AddressStage$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].missing_addresses,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$utils$2f$alert$2f$Alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        variant: "warning",
                                        textSize: "20",
                                        message: " You haven't added a shipping address yet.  Please add one along with a phone number to continue with checkout. Thank you! 😊"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                        lineNumber: 85,
                                        columnNumber: 25
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                                        className: "btn_container",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            className: "empty_btn",
                                            onClick: ()=>router.push('/account/profile'),
                                            children: "Update Profile"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 93,
                                            columnNumber: 27
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                        lineNumber: 92,
                                        columnNumber: 25
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                lineNumber: 84,
                                columnNumber: 23
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: !data || data.cart_items.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$checkout$2f$address$2d$choice$2f$AddressStage$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].empty_cart,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: "Your cart is empty. Add some products to the cart to checkout!"
                                }, void 0, false, {
                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                    lineNumber: 106,
                                    columnNumber: 27
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/",
                                    children: "Go Shopping "
                                }, void 0, false, {
                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                    lineNumber: 110,
                                    columnNumber: 27
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                            lineNumber: 105,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                                    className: "container ".concat(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$checkout$2f$address$2d$choice$2f$AddressStage$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].address_stage),
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            children: "Address Choices"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 118,
                                            columnNumber: 31
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$checkout$2f$address$2d$choice$2f$AddressStage$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].contact_details,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    children: "Contact Details: "
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 120,
                                                    columnNumber: 33
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "Deliver to: ",
                                                        customer === null || customer === void 0 ? void 0 : customer.first_name,
                                                        ' ',
                                                        customer === null || customer === void 0 ? void 0 : customer.last_name
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 121,
                                                    columnNumber: 33
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "Phone: ",
                                                        customer === null || customer === void 0 ? void 0 : customer.phone_number
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 125,
                                                    columnNumber: 33
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "Email to: ",
                                                        customer === null || customer === void 0 ? void 0 : customer.email
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 126,
                                                    columnNumber: 33
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 119,
                                            columnNumber: 31
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 128,
                                            columnNumber: 31
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$checkout$2f$address$2d$choice$2f$AddressStage$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].cart,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$cart$2f$CartItemsList$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    cartItems: data.cart_items
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 130,
                                                    columnNumber: 33
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$components$2f$price$2d$summary$2f$PriceSummary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    totalPrice: data === null || data === void 0 ? void 0 : data.total_price,
                                                    // shippingCost={data?.shipping_cost}
                                                    // grandTotal={data?.grand_total}
                                                    item_count: data === null || data === void 0 ? void 0 : data.item_count,
                                                    cart_weight: data === null || data === void 0 ? void 0 : data.cart_weight
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 131,
                                                    columnNumber: 33
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 129,
                                            columnNumber: 31
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 139,
                                            columnNumber: 31
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        addressesReady && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$shop$292f28$checkout$2d$process$292f$checkout$2f$address$2d$choice$2f$AddressStage$2e$module$2e$scss__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].address_selection,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    children: "Choose a shipping address: "
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 143,
                                                    columnNumber: 35
                                                }, ("TURBOPACK compile-time value", void 0)),
                                                customer === null || customer === void 0 ? void 0 : (_customer_address1 = customer.address) === null || _customer_address1 === void 0 ? void 0 : _customer_address1.map((address)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("address", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                type: "radio",
                                                                id: "address-".concat(address.id),
                                                                name: "address",
                                                                value: address.id,
                                                                checked: (selectedAddress === null || selectedAddress === void 0 ? void 0 : selectedAddress.id) === address.id,
                                                                onChange: ()=>handleAddressChange(address)
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                                lineNumber: 146,
                                                                columnNumber: 39
                                                            }, ("TURBOPACK compile-time value", void 0)),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                htmlFor: "address-".concat(address.id),
                                                                children: [
                                                                    address.full_name,
                                                                    ",",
                                                                    ' ',
                                                                    address.street_name,
                                                                    ",",
                                                                    ' ',
                                                                    address.city_or_village
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                                lineNumber: 158,
                                                                columnNumber: 39
                                                            }, ("TURBOPACK compile-time value", void 0))
                                                        ]
                                                    }, address.id, true, {
                                                        fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                        lineNumber: 145,
                                                        columnNumber: 37
                                                    }, ("TURBOPACK compile-time value", void 0))),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>router.push('/checkout/payment-choice'),
                                                    children: "Use this address"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                                    lineNumber: 165,
                                                    columnNumber: 35
                                                }, ("TURBOPACK compile-time value", void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 142,
                                            columnNumber: 33
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                            fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                            lineNumber: 174,
                                            columnNumber: 31
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                    lineNumber: 115,
                                    columnNumber: 29
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/app/(shop)/(checkout-process)/checkout/address-choice/page.tsx",
                                lineNumber: 114,
                                columnNumber: 27
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false)
                    }, void 0, false)
                }, void 0, false)
            }, void 0, false)
        }, void 0, false)
    }, void 0, false);
};
_s(AddressChoice, "0xolU9IsS78Wz6hynW/slWZk4kw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$customer$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCustomerDetails"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCart"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$cart$2d$hooks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDeleteCartItem"]
    ];
});
_c = AddressChoice;
const __TURBOPACK__default__export__ = AddressChoice;
var _c;
__turbopack_context__.k.register(_c, "AddressChoice");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
]);

//# sourceMappingURL=_a96b3a6e._.js.map