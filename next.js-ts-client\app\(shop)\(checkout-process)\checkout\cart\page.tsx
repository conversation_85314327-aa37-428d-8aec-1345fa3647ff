'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import styles from './Cart.module.scss'
import { AxiosError } from 'axios'
import {
  useCart,
  useDeleteCartItem,
  useUpdateCart,
} from '@/src/hooks/cart-hooks'
import { CartItemShape } from '@/src/types/store-types'
import Alert from '@/src/components/utils/alert/Alert'
import { getErrorMessage } from '@/src/components/utils/getErrorMessage'
import { ErrorResponse } from '@/src/types/types'
import CheckoutPageWrapper from '../../components/layout/CheckoutPageWrapper'
import CartDisplaySection from '../../components/layout/CartDisplaySection'

const Cart = () => {
  const router = useRouter()
  const [noStockAlert, setNoStockAlert] = useState(false)

  const { data } = useCart()
  const { handleQuantityUpdate, mutation } = useUpdateCart()
  const { mutate: deleteCartItem } = useDeleteCartItem()

  const handleIncrement = (item: CartItemShape) => {
    const newQuantity = item.quantity + 1
    handleQuantityUpdate(item.id, newQuantity)
  }

  const handleDecrement = (item: CartItemShape) => {
    if (item.quantity > 1) {
      const newQuantity = item.quantity - 1
      handleQuantityUpdate(item.id, newQuantity)
    }
  }

  const handleCheckout = (cartItems: CartItemShape[]) => {
    const hasOutOfStockItems = cartItems.some(
      (item) => item.product_variant.stock_qty === 0
    )
    if (hasOutOfStockItems) {
      setNoStockAlert(true)
    } else {
      router.push('/checkout/address-choice')
    }
  }

  useEffect(() => {
    // When cart data changes, check if there are still any out-of-stock items.
    if (data && data?.cart_items?.length > 0) {
      const stillOutOfStock = data?.cart_items.some(
        (item) => item.product_variant.stock_qty === 0
      )
      // Hide the alert if no items are out of stock.
      if (!stillOutOfStock) {
        setNoStockAlert(false)
      }
    }
  }, [data])

  return (
    <CheckoutPageWrapper requireAuth={false}>
      <div>
        {noStockAlert && (
          <Alert
            variant='error'
            message={`
        Some items are out of stock. Please remove them from the cart to proceed with checkout process.`}
          />
        )}

        {mutation.error && (
          <Alert
            variant='error'
            message={getErrorMessage(
              mutation.error as AxiosError<ErrorResponse>
            )}
          />
        )}

        {data && (
          <div className={`${styles.cart} container`}>
            <h2>Shopping Cart</h2>
            <div className={styles.cart__cart_items}>
              <CartDisplaySection
                cartItems={data.cart_items}
                totalPrice={data.total_price}
                itemCount={data.item_count}
                cartWeight={data.cart_weight}
                handleIncrement={handleIncrement}
                handleDecrement={handleDecrement}
                deleteCartItem={deleteCartItem}
                onCheckout={() => handleCheckout(data.cart_items)}
                showCheckoutButton={true}
              />
            </div>
          </div>
        )}
      </div>
    </CheckoutPageWrapper>
  )
}

export default Cart
