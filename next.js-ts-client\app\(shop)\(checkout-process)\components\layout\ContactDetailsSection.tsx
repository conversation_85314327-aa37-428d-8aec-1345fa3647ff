import { CustomerShape } from '@/src/types/store-types'
import { AddressFormInputs } from '@/src/components/account/addresses/ManageAddresses'
import styles from './ContactDetailsSection.module.scss'

interface ContactDetailsSectionProps {
  customer: CustomerShape | undefined
  selectedAddress?: AddressFormInputs
  showShippingAddress?: boolean
}

const ContactDetailsSection = ({
  customer,
  selectedAddress,
  showShippingAddress = false,
}: ContactDetailsSectionProps) => {
  return (
    <section className={styles.contact_info}>
      <div className={styles.contact_details}>
        <h3>Contact Details: </h3>
        <p>
          Deliver to: {customer?.first_name} {customer?.last_name}
        </p>
        <p>Phone: {customer?.phone_number}</p>
        <p>Email to: {customer?.email}</p>
      </div>

      {showShippingAddress && selectedAddress && (
        <div className={styles.shipping_address}>
          <h3>Shipping address: </h3>
          <address>
            {selectedAddress.full_name},<br />
            {selectedAddress.street_name},<br />
            {selectedAddress.postal_code},<br />
            {selectedAddress.city_or_village}
            <br />
          </address>
        </div>
      )}
    </section>
  )
}

export default ContactDetailsSection
