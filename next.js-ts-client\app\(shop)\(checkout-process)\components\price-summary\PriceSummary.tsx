import React from 'react'
import { RiQuestionFill } from 'react-icons/ri'
import styles from './PriceSummary.module.scss'
import Tooltip from '@/src/components/utils/tooltip/Tooltip'

interface PriceSummaryProps {
  totalPrice: number
  // shippingCost: number
  // grandTotal: number
  item_count: number
  cart_weight: number
  onCheckout?: () => void
}

const PriceSummary: React.FC<PriceSummaryProps> = ({
  totalPrice,
  item_count,
  cart_weight,
  onCheckout,
}) => {
  return (
    <div className={styles.cart__checkout}>
      <div>
        {/* <p>Total: </p> <p>${grandTotal.toFixed(2)}</p> */}
        <p>Item count: </p> <p>{item_count}</p>
      </div>
      <div>
        {/* <p>Shipping cost:
          <Tooltip
            content={`Cost for packaging & weight of cart items (${cart_weight}g)`}
            position="top"
          >
            <i><RiQuestionFill /></i>
          </Tooltip>
        </p>
        <p>${shippingCost.toFixed(2)}</p> */}

        <p>
          Cart weight:
          <Tooltip
            content={`Shipping cost will be calculated after finalizing adding items to the cart.`}
            position='top'
          >
            <i>
              <RiQuestionFill />
            </i>
          </Tooltip>
        </p>
        <p>{cart_weight}g</p>

        {/* <p>Cart weight: </p> <p>{cart_weight}g</p> */}
      </div>

      <div>
        <p>Item&apos;s cost: </p> <p>${totalPrice.toFixed(2)}</p>
      </div>
      {onCheckout && <button onClick={onCheckout}>Proceed to checkout</button>}
    </div>
  )
}

export default PriceSummary
