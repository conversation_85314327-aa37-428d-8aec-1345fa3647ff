{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/app/(shop)/(checkout-process)/checkout/payment-choice/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/app/(shop)/(checkout-process)/checkout/payment-choice/PaymentChoice.module.scss", "turbopack:///turbopack:///[project]/app/(shop)/(checkout-process)/checkout/payment-choice/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_variables.scss", "turbopack:///turbopack:///[project]/app/(shop)/(checkout-process)/checkout/payment-choice/C:/Users/<USER>/Downloads/Projects/Projects Repos/picky-ecommerce-app/picky-store/next.js-ts-client/src/scss/_mixins.scss"], "sourcesContent": ["@use '../../../../../src/scss/variables' as *;\n@use '../../../../../src/scss/mixins' as *;\n\n.place_order {\n  background-color: $sky-lighter-blue;\n  font-weight: bold;\n  color: $primary-dark-text-color;\n  text-align: center;\n  padding: 0.5rem 0;\n  font-size: 25px;\n}\n\n.payment_options_stage {\n  display: grid;\n  grid-template-columns: 1fr;\n}\n\n.logo {\n  background-color: $primary-dark;\n  padding: 10px 0;\n  @include flexbox(center, center);\n}\n\n.contact_info {\n  @include flexbox(space-around, center);\n  padding: 1rem 0;\n\n  div {\n    h3 {\n      font-weight: bold;\n      color: $primary-blue;\n      font-size: 18px;\n    }\n  }\n}\n\n.contact_details,\n.shipping_address {\n  h3 {\n    font-weight: bold;\n  }\n\n  address {\n    font-style: normal;\n  }\n}\n\n.cart {\n  display: grid;\n  grid-template-columns: 1fr;\n  gap: 1rem;\n  margin: 1rem 0;\n}\n\n.cart_items_quantity {\n  @include flexbox(center, center);\n  column-gap: 0.5rem;\n  font-weight: bold;\n}\n\n.payment_options {\n  padding: 0 0 0 1rem;\n  margin: 1rem auto;\n\n  div {\n    margin: 0 0 0 2rem;\n    @include flexbox(flex-start, center);\n    column-gap: 0.4rem;\n\n    input {\n      margin: 0.5rem 0;\n    }\n  }\n\n  h3 {\n    font-weight: bold;\n    margin: 10px 0;\n    color: $primary-blue;\n    font-size: 18px;\n  }\n}\n\n.price_summary {\n  background-color: $sky-lighter-blue;\n  // padding: 0 1.5rem;\n  @include flexbox(flex-start, flex-start, column);\n  gap: 1.5rem;\n\n  .prices {\n    // @include flexJustify(space-around);\n    @include flexbox(flex-start, flex-start, column);\n    font-size: $font-size-3;\n    row-gap: 1rem;\n\n    div {\n      @include flexbox(space-between, center);\n    }\n\n    div:last-child {\n      p:last-child {\n        font-size: $font-size-4;\n      }\n    }\n\n    p:first-child {\n      font-weight: bold;\n      color: $primary-dark-text-color;\n    }\n\n    p:last-child {\n      background-color: #e0adad;\n      font-weight: bold;\n      color: $primary-blue;\n    }\n  }\n\n  button {\n    @include btn(#fff, $lighten-blue);\n    width: calc(100% - 3rem);\n    padding: 1rem 0rem;\n    font-weight: bold;\n    text-transform: uppercase;\n    letter-spacing: 0.7px;\n    transition: all 0.3s ease;\n    margin: 0 1.5rem;\n\n    &:disabled {\n      background-color: #ccc;\n      color: #666;\n      cursor: not-allowed;\n    }\n\n    &:hover {\n      background-color: darken($lighten-blue, 10%);\n      color: darken(#fff, 15%);\n    }\n  }\n}\n\n@media (width > $tablet) {\n  .payment_options_stage {\n    grid-template-columns: 2fr 1fr;\n    gap: 1rem;\n    margin: 1rem 0 0 0;\n  }\n\n  .contact_info {\n    column-gap: 4rem;\n    @include flexbox(flex-start, flex-start);\n    padding: 0 0 1rem 0;\n  }\n}\n", "// Fonts\r\n$primary-font-family: '<PERSON> Sans', '<PERSON>s MT', <PERSON><PERSON><PERSON>, 'Trebuchet MS',\r\n  sans-serif;\r\n\r\n// Font Size\r\n$font-size-1: 12px;\r\n$font-size-2: 14px;\r\n$font-size-3: 16px;\r\n$font-size-4: 18px;\r\n$font-size-5: 20px;\r\n$font-size-6: 22px;\r\n$font-size-7: 24px;\r\n$font-size-8: 32px;\r\n\r\n// Font Weight for Amazon Ember font (Using SCSS Maps)\r\n$font-weight: (\r\n  'thin': 300,\r\n  'regular': 400,\r\n  'light': 500,\r\n  'medium': 600,\r\n  'bold': 700,\r\n  'heavy': 800,\r\n);\r\n\r\n// Colors\r\n$primary-dark: #131921;\r\n\r\n$primary-yellow: #ffd814;\r\n$lighten-yellow: #ffe180;\r\n\r\n$primary-red: #cf0707;\r\n$error-red-bg: #ff9494;\r\n$error-red: #f00000;\r\n\r\n$primary-blue: #0091cf;\r\n$lighten-blue: #00b3ff;\r\n$sky-light-blue: #a4e4ff;\r\n$sky-lighter-blue: #d4f4ff;\r\n\r\n$primary-dark-blue: #232f3e;\r\n\r\n$primary-green: #2e9f1c;\r\n\r\n$primary-dark-text-color: #333;\r\n$primary-lighter-text-color: #666;\r\n\r\n$info-bg: #bedeff;\r\n$info-text: #084298;\r\n\r\n$warning-bg: #ffe180;\r\n$warning-text: #534000;\r\n\r\n$error-bg: #ffb2b9;\r\n$error-text: #580007;\r\n\r\n$success-bg: #9fffa3;\r\n$success-text: #002e02;\r\n\r\n// Box shadow\r\n$box-shadow-1: 0px 0px 0px 1px #0000000d, 0px 0px 0px 1px inset #d1d5db;\r\n$box-shadow-2: 0px 0px 0px 5px #0000000d, 0px 0px 0px 2px inset #d1d5db;\r\n$box-shadow-blue-1: #0091cf66 0px 0px 0px 2px, #0091cfa6 0px 4px 6px -1px;\r\n\r\n// Border radius\r\n$border-radius-1: 3px;\r\n$border-radius-2: 5px;\r\n$border-radius-3: 8px;\r\n$border-radius-4: 10px;\r\n\r\n// Padding\r\n$padding-1: 5px;\r\n$padding-2: 10px;\r\n$padding-3: 12px;\r\n$padding-4: 15px;\r\n$padding-5: 20px;\r\n\r\n// Media queries\r\n$mobile: 576px;\r\n$tablet: 768px;\r\n$laptop: 992px;\r\n$monitor: 1200px;\r\n", "@use './variables' as *;\n\n// Generic flexbox mixin\n@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {\n  display: flex;\n  justify-content: $justify;\n  align-items: $align;\n  flex-direction: $direction;\n  flex-wrap: $wrap;\n}\n\n\n@mixin btn($color, $bg-color) {\n  @include flexbox(center, center);\n  color: $color;\n  background-color: $bg-color;\n  padding: 5px 10px;\n  border-radius: 3px;\n}\n\n// @mixin theme($light-theme: true) {\n//   @if $light-theme {\n//     background-color: lighten($primary-dark, 100%);\n//     color: darken($text-color, 100%);\n//   }\n// }\n\n// add this class \n// .light {\n//   @include theme(true);\n//   // @include theme($light-theme: true);\n// }\n\n\n@mixin mobile {\n  @media (max-width: $mobile) {\n    @content;\n  }\n}\n\n@include mobile {\n  // define what do you wanna do below 430px (mobile)\n  // ex: flex-direction: column;\n}\n\n// Dynamic Mixin for List Styling\n@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {\n  @for $i from 0 through $max-levels {\n    .level-#{$i} {\n      margin-left: $i * $gap;\n      list-style: circle;\n      color: darken($base-color, $i * 10%);\n\n      // Example: Different list styles for different levels\n      @if $i % 3==0 {\n        list-style: disc;\n      }\n\n      @else if $i % 3==1 {\n        list-style: square;\n      }\n\n      @else {\n        list-style: none;\n      }\n\n      // Adjust for flex styles\n      @if $i ==0 {\n        @include flexbox(flex-start, flex-start, row);\n        flex-wrap: wrap;\n      }\n\n      @else {\n        display: block;\n      }\n    }\n  }\n}\n\n\n\n// Extensions in scss"], "names": [], "mappings": "AAGA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;AAMA;;;;;;;;AAKI;;;;;;AAUF;;;;AAIA;;;;AAKF;;;;;;;AAOA;;;;;;;;;AAMA;;;;;AAIE;;;;;;;;;AAKE;;;;AAKF;;;;;;;AAQF;;;;;;;;;AAME;;;;;;;;;AAME;;;;;;;AAKE;;;;AAKF;;;;;AAKA;;;;;;AAOF;;;;;;;;;;;;;;;;;AAUE;;;;;;AAMA;;;;;AAOJ;EACE;;;;;;EAMA"}}]}