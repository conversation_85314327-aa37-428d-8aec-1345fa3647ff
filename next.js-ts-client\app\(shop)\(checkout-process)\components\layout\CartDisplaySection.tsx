import { CartItemShape } from '@/src/types/store-types'
import CartItemsList from '../cart/CartItemsList'
import PriceSummary from '../price-summary/PriceSummary'
import styles from './CartDisplaySection.module.scss'

interface CartDisplaySectionProps {
  cartItems: CartItemShape[]
  totalPrice: number
  itemCount: number
  cartWeight: number
  handleIncrement?: (item: CartItemShape) => void
  handleDecrement?: (item: CartItemShape) => void
  deleteCartItem?: (itemId: number) => void
  onCheckout?: () => void
  showCheckoutButton?: boolean
}

const CartDisplaySection = ({
  cartItems,
  totalPrice,
  itemCount,
  cartWeight,
  handleIncrement,
  handleDecrement,
  deleteCartItem,
  onCheckout,
  showCheckoutButton = false
}: CartDisplaySectionProps) => {
  return (
    <div className={styles.cart_display}>
      <CartItemsList
        cartItems={cartItems}
        handleIncrement={handleIncrement}
        handleDecrement={handleDecrement}
        deleteCartItem={deleteCartItem}
      />
      <PriceSummary
        totalPrice={totalPrice}
        item_count={itemCount}
        cart_weight={cartWeight}
        onCheckout={showCheckoutButton ? onCheckout : undefined}
      />
    </div>
  )
}

export default CartDisplaySection
